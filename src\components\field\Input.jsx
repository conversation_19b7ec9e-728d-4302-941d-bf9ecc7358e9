import PropTypes from "prop-types";
// import { motion } from "framer-motion";

const Input = ({
  type,
  name,
  label,
  disabled,
  value,
  defaultValue,
  onChange,
  onClick,
  accept,
  max,
  min,
  maxLength,
  minLength,
  pattern,
  required,
  placeholder,
  className,
  error,
  leftIcon,
  rightIcon,
  checked,
  inputmode,
  autoComplete,
  title,
}) => {
  if (type === "checkbox") {
    return (
      <label className={`inline-flex items-center ${className || ""}`}>
        <input
          type="checkbox"
          name={name}
          disabled={disabled}
          checked={checked}
          onChange={onChange}
          onClick={onClick}
          className={`form-checkbox h-5 w-5 text-indigo-600 ${
            disabled ? "cursor-not-allowed" : "cursor-pointer"
          } rounded border-gray-300 focus:ring-indigo-500`}
        />
        {label && (
          <span
            className={`ml-2 text-gray-700 ${disabled ? "text-gray-500" : ""}`}
          >
            {label}
            {required && (
              <span
                id="required"
                className="text-lg font-semibold text-red-600 ml-1"
              >
                *
              </span>
            )}
          </span>
        )}
      </label>
    );
  }

  return (
    <div className="relative inline-block w-full">
      {label && (
        <label
          htmlFor={name}
          className="tracking-wide block mb-1 font-semibold"
        >
          {label}
          {required && (
            <span id="required" className="text-lg font-bold text-red-600 ml-1">
              *
            </span>
          )}
        </label>
      )}

      {leftIcon && (
        <span className="absolute inset-y-0 left-3 flex items-center text-gray-500">
          {leftIcon}
        </span>
      )}
      <input
        type={type}
        name={name}
        disabled={disabled}
        value={value}
        defaultValue={defaultValue}
        onChange={onChange}
        onClick={onClick}
        accept={accept}
        max={max}
        min={min}
        maxLength={maxLength}
        minLength={minLength}
        pattern={pattern}
        required={required}
        placeholder={placeholder}
        checked={checked}
        inputMode={inputmode}
        autoComplete={autoComplete}
        title={title}
        className={`w-full py-3 text-black ${leftIcon ? "pl-12" : "px-4"} ${
          rightIcon ? "pr-12" : "px-4"
        } border-2 focus:ring-gray-500 focus:border-gray-500 outline-none placeholder:text-gray-500
            ${className || "rounded-md"}
            ${
              error
                ? "border-red-500 focus:border-red-500 focus:ring-red-500"
                : "border-gray-300"
            }
            ${disabled ? "cursor-not-allowed bg-gray-200" : ""}`}
      />
      {rightIcon && (
        <span className="absolute inset-y-0 right-3 flex items-center text-gray-500">
          {rightIcon}
        </span>
      )}
    </div>
  );
};

Input.propTypes = {
  type: PropTypes.string.isRequired,
  name: PropTypes.string,
  label: PropTypes.string,
  disabled: PropTypes.bool,
  value: PropTypes.oneOfType([PropTypes.string, PropTypes.number]),
  defaultValue: PropTypes.oneOfType([PropTypes.string, PropTypes.number]),
  onChange: PropTypes.func,
  onClick: PropTypes.func,
  accept: PropTypes.string,
  max: PropTypes.oneOfType([PropTypes.string, PropTypes.number]),
  min: PropTypes.oneOfType([PropTypes.string, PropTypes.number]),
  maxLength: PropTypes.number,
  minLength: PropTypes.number,
  pattern: PropTypes.string,
  required: PropTypes.bool,
  placeholder: PropTypes.string,
  className: PropTypes.string,
  error: PropTypes.bool,
  leftIcon: PropTypes.node,
  rightIcon: PropTypes.node,
  checked: PropTypes.bool,
  inputmode: PropTypes.string,
  autoComplete: PropTypes.string,
  title: PropTypes.string,
};

export default Input;
