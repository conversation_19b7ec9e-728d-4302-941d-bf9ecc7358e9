import { useState } from "react";
import { motion, AnimatePresence } from "framer-motion"; // Add this import
import Button from "../../components/field/Button";
import Input from "../../components/field/Input";
import { AiOutlineEye, AiOutlineEyeInvisible } from "react-icons/ai";
import { IoArrowBack } from "react-icons/io5";  // Make sure this import exists at the top

import { useNavigate } from "react-router-dom";
import loginImage from "../../assets/boypose.svg"; // Make sure to add your image

import Toastify from "../../components/popup/Toastify";
import Service from "../../services/Service";

const SignIn = () => {
  const navigate = useNavigate();
  const [formData, setFormData] = useState({
    email: "",
    password: "",
  });
  const [showPassword, setShowPassword] = useState(false);
  const [error, setError] = useState(""); // Add error state
  const [res, setRes] = useState(null);
  const [isSigningIn, setIsSigningIn] = useState(false);
  const togglePasswordVisibility = () => {
    setShowPassword(!showPassword);
  };

  const handleChange = (e) => {
    const { name, value } = e.target;
    setFormData((prev) => ({
      ...prev,
      [name]: value,
    }));
    setError(""); // Clear error when user types
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    setIsSigningIn(true);
    try {
      const response = await Service.userLoginService(formData);
      setRes(response);
      const { user_id, session_id } = response.data;
      sessionStorage.setItem("user_id", user_id);
      sessionStorage.setItem("user_email", formData.email);
      sessionStorage.setItem("session_id", session_id);
      sessionStorage.setItem("isAuthenticated", "true");

      // Add delay for animation
      setTimeout(() => {
        navigate("/dashboard");
      }, 1500);
    } catch (error) {
      setIsSigningIn(false);
      if (error.response) {
        setRes(error);
        setError(error.response.data.message);
      } else {
        setError("An error occurred. Please try again.");
      }
    }
  };

  return (
    <motion.div
      initial={{ opacity: 0 }}
      animate={{ opacity: 1 }}
      className="min-h-screen flex items-center justify-center bg-[var(--text-color)] relative overflow-hidden"
    >
      {/* Back Button */}
      <Button
        link="/"
        className="absolute top-6 left-6 w-[100px] h-[50px] flex items-center gap-2 bg-white px-4 py-2 rounded-lg shadow-md hover:shadow-lg transition-all z-10"
      >
        <IoArrowBack className="text-2xl text-[var(--primary-color)]" />
        <span className="font-medium">Back to Home</span>
      </Button>

      {/* Remove duplicate back button and continue with rest of the code */}
      <AnimatePresence>
        {isSigningIn && (
          <>
            {[...Array(20)].map((_, i) => (
              <motion.div
                key={i}
                initial={{
                  opacity: 0,
                  x: Math.random() * window.innerWidth,
                  y: window.innerHeight + 100,
                }}
                animate={{
                  opacity: 0.3,
                  y: -100,
                  x: Math.random() * window.innerWidth,
                  rotate: 360,
                }}
                exit={{ opacity: 0 }}
                transition={{
                  duration: 1.5,
                  delay: i * 0.1,
                  ease: "easeOut",
                }}
                className={`absolute w-8 h-8 bg-[var(--primary-color)] rounded-full`}
              />
            ))}
          </>
        )}
      </AnimatePresence>

      {/* Back Button */}
            <Button
              link="/"
              className="absolute top-6 left-6 flex items-center gap-2 bg-[var(--primary-color)] px-4 py-2 rounded-lg shadow-md hover:shadow-lg transition-all"
            >
              <IoArrowBack className="text-2xl text-[var(--primary-color)]" />
              <span className="font-medium text-[var(--primary-color)]">Back to Home</span>
            </Button>

      {/* Existing back button */}
      <Toastify res={res} resClear={() => setRes(null)} />

      <motion.div
        className="container mx-auto"
        animate={
          isSigningIn
            ? {
                scale: [1, 0.9],
                opacity: [1, 0.5],
              }
            : {}
        }
        transition={{ duration: 0.5 }}
      >
        <motion.div
          className="flex flex-col md:flex-row items-center justify-between bg-[var(--text-color)] rounded-xl shadow-lg overflow-hidden"
          animate={
            isSigningIn
              ? {
                  y: [0, 20],
                  opacity: [1, 0.8],
                }
              : {}
          }
        >
          {/* Left Side - Form */}
          <div className="w-full md:w-1/2 p-8 lg:p-12">
            <div>
              <h2 className="text-3xl font-extrabold text-[var(--secondary-color)]">
                Welcome Back
              </h2>
              {/* <p className="mt-2 text-sm text-gray-600">
                Don't have an account?{" "}
                <Button
                  name="Sign Up"
                  link="/signup"
                  className="text-[var(--primary-color)] cursor-pointer hover:text-opacity-80 font-medium"
                />
              </p> */}
            </div>

            <form className="mt-8 space-y-6" onSubmit={handleSubmit}>
              <div className="space-y-4">
                <Input
                  type="email"
                  name="email"
                  label="Email address"
                  value={formData.email}
                  onChange={handleChange}
                  required
                  placeholder="Enter your email"
                />

                <Input
                  type={showPassword ? "text" : "password"}
                  name="password"
                  label="Password"
                  value={formData.password}
                  onChange={handleChange}
                  required
                  placeholder="Enter your password"
                  rightIcon={
                    <button
                      type="button"
                      onClick={togglePasswordVisibility}
                      className="focus:outline-none"
                    >
                      {showPassword ? (
                        <AiOutlineEyeInvisible className="w-5 h-10 text-[var(--primary-color)] font-bold mt-8 cursor-pointer" />
                      ) : (
                        <AiOutlineEye className="w-5 h-10 text-[var(--primary-color)] mt-8 font-bold cursor-pointer" />
                      )}
                    </button>
                  }
                />

                <div className="flex items-center justify-between">
                  <Input
                    type="checkbox"
                    name="remember"
                    label="Remember me"
                    className="text-sm"
                  />
                  <Button
                    name="Forgot Password?"
                    link="/forgot-password"
                    className="text-sm cursor-pointer text-[var(--primary-color)] hover:text-opacity-80"
                  />
                </div>
              </div>

              <Button
                type="submit"
                name={isSigningIn ? "Signing In..." : "Sign In"}
                className={`w-full cursor-pointer bg-[var(--primary-color)] hover:opacity-90 text-[var(--text-color)] py-3 rounded-lg font-medium transition-all ${
                  isSigningIn ? "opacity-70" : ""
                }`}
                disabled={isSigningIn}
              />
            </form>
          </div>

          {/* Right Side - Image */}
          <motion.div
            className="hidden md:block w-1/2 p-12 bg-[var(--text-color)]"
            animate={
              isSigningIn
                ? {
                    scale: [1, 1.1],
                    y: [0, -20],
                  }
                : {}
            }
            transition={{ duration: 0.5 }}
          >
            <div className="relative h-full">
              <motion.div
                className="absolute w-[400px] h-[400px] bg-[var(--primary-color)] rounded-full -z-10 opacity-10"
                animate={
                  isSigningIn
                    ? {
                        scale: [1, 1.5],
                        opacity: [0.1, 0.3],
                      }
                    : {}
                }
              />
              <motion.img
                src={loginImage}
                alt="Login"
                className="relative z-10 w-full h-full object-contain"
                animate={
                  isSigningIn
                    ? {
                        y: [0, -30],
                        scale: [1, 1.1],
                      }
                    : {}
                }
              />
            </div>
          </motion.div>
        </motion.div>
      </motion.div>

      {/* Success Animation Overlay */}
      <AnimatePresence>
        {isSigningIn && (
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            className="fixed inset-0 bg-[var(--primary-color)]/20 backdrop-blur-sm z-50 flex items-center justify-center"
          >
            <motion.div
              initial={{ scale: 0 }}
              animate={{ scale: 1 }}
              className="bg-white p-8 rounded-full"
            >
              <motion.div
                animate={{
                  rotate: 360,
                  scale: [1, 1.2, 1],
                }}
                transition={{
                  duration: 2,
                  repeat: Infinity,
                  ease: "easeInOut",
                }}
                className="w-16 h-16 border-4 border-[var(--primary-color)] border-t-transparent rounded-full"
              />
            </motion.div>
          </motion.div>
        )}
      </AnimatePresence>
    </motion.div>
  );
};

export default SignIn;
