import Button from "../../components/field/Button";
import TestLayout from "./components/TestLayout";

const Speaking = () => {
  const academicTests = [
    "Read Aloud",
    "Repeat Sentence",
    "Describe Image",
    "Re-tell Lecture",
    "Answer Short Question",
  ];

  const coreTests = [
    "Read Aloud",
    "Repeat Sentence",
    "Describe Image",
    "Answer Short Question",
  ];

  return (
    <TestLayout section="Speaking Practice">
      {({ examType }) => (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {(examType === "academic" ? academicTests : coreTests).map(
            (test, index) => (
              <div
                key={index}
                className="bg-white p-6 rounded-xl shadow-sm hover:shadow-md transition-all cursor-pointer"
              >
                <h3 className="font-semibold text-lg mb-2">{test}</h3>
                <p className="text-gray-500 text-sm">
                  Practice your speaking skills with our AI-powered evaluation
                  system
                </p>
                <div className="mt-4 flex justify-between items-center">
                  <span className="text-sm text-gray-400">0 Attempts</span>
                  <Button
                    name="Start Practice"
                    className="bg-[var(--primary-color)] text-white px-4 py-2 rounded-lg text-sm"
                  />
                </div>
              </div>
            )
          )}
        </div>
      )}
    </TestLayout>
  );
};

export default Speaking;
