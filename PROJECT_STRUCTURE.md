# Complete Project Structure - PTE UI Frontend

## Root Directory Structure
```
pte_ui/
├── config/                          # Environment configuration files
│   ├── .env.development             # Development environment variables
│   └── .env.production              # Production environment variables
├── docker/                          # Docker configuration
│   └── Dockerfile                   # Docker build configuration
├── src/                             # Source code directory
│   ├── assets/                      # Static assets (images, icons, etc.)
│   ├── components/                  # Reusable React components
│   │   ├── common/                  # Common utilities and constants
│   │   │   └── Name.js              # Project name constant
│   │   ├── field/                   # Form field components
│   │   │   ├── Button.jsx           # Reusable button component
│   │   │   ├── DatePickerComponents.jsx  # Date picker component
│   │   │   ├── Input.jsx            # Input field component
│   │   │   └── PhoneInputComponent.jsx   # Phone number input
│   │   ├── layout/                  # Layout components
│   │   │   ├── Navbar.jsx           # Main navigation bar
│   │   │   ├── Sidebar.jsx          # Dashboard sidebar
│   │   │   └── Topbar.jsx           # Dashboard top bar
│   │   └── popup/                   # Popup and notification components
│   │       └── Toastify.jsx         # Toast notification component
│   ├── pages/                       # Page components
│   │   ├── authentication/          # Authentication pages
│   │   │   ├── ForgetPassword.jsx   # Forgot password page
│   │   │   ├── Hero.jsx             # Authentication hero component
│   │   │   ├── Logout.jsx           # Logout page
│   │   │   ├── ResetPassword.jsx    # Reset password page
│   │   │   ├── SessionValidation.jsx # Session validation component
│   │   │   ├── SignIn.jsx           # Sign in page
│   │   │   └── SignUp.jsx           # Sign up page
│   │   ├── dashboard/               # Dashboard pages
│   │   │   ├── components/          # Dashboard specific components
│   │   │   │   └── TestLayout.jsx   # Test layout component
│   │   │   ├── reading/             # Reading module pages
│   │   │   │   ├── Reading.jsx      # Main reading exam page
│   │   │   │   ├── ReadingEvaluation.jsx    # Reading evaluation logic
│   │   │   │   ├── ReadingInstructions.jsx  # Reading instructions page
│   │   │   │   ├── ReadingPractice.jsx      # Reading practice page
│   │   │   │   ├── ReadingQuestionRenderer.jsx  # Question renderer
│   │   │   │   └── ReadingQuestionService.jsx   # Question service
│   │   │   ├── DashboardLayout.jsx  # Dashboard layout wrapper
│   │   │   ├── Listening.jsx        # Listening module page
│   │   │   ├── Overview.jsx         # Dashboard overview page
│   │   │   ├── Speaking.jsx         # Speaking module page
│   │   │   └── Writing.jsx          # Writing module page
│   │   ├── home/                    # Home page components
│   │   │   ├── About.jsx            # About section
│   │   │   ├── Contact.jsx          # Contact section
│   │   │   ├── Footer.jsx           # Footer component
│   │   │   ├── Hero.jsx             # Hero section
│   │   │   └── Testimonial.jsx      # Testimonials section
│   │   └── productedroute/          # Protected route components
│   │       ├── Monitor.jsx          # Monitoring component
│   │       └── ProductedRoute.jsx   # Protected route wrapper
│   ├── services/                    # API service modules
│   │   ├── authentication/          # Authentication services
│   │   │   └── index.js             # Auth API calls
│   │   ├── exammodule/              # Exam module services
│   │   │   └── index.js             # Exam API calls
│   │   ├── monitor/                 # Monitoring services
│   │   │   └── index.js             # Monitor API calls
│   │   ├── payment/                 # Payment services
│   │   │   └── index.js             # Payment API calls
│   │   └── Service.js               # Main service aggregator
│   ├── App.jsx                      # Main App component
│   ├── config.js                    # Configuration file
│   ├── index.css                    # Global CSS styles
│   └── main.jsx                     # React entry point
├── .env.local                       # Local environment variables
├── .gitignore                       # Git ignore file
├── .gitlab-ci.yml                   # GitLab CI/CD configuration
├── eslint.config.js                 # ESLint configuration
├── index.html                       # HTML template
├── package.json                     # NPM dependencies and scripts
├── README.md                        # Project documentation
└── vite.config.js                   # Vite build configuration
```

## Key Configuration Files

### Environment Files
- **`.env.local`** - Local development environment variables
- **`config/.env.development`** - Development environment configuration
- **`config/.env.production`** - Production environment configuration

### Build & Development
- **`vite.config.js`** - Vite bundler configuration with React and Tailwind
- **`package.json`** - Dependencies and build scripts
- **`eslint.config.js`** - Code linting rules
- **`tailwind.config.js`** - Tailwind CSS configuration (if exists)

### Deployment
- **`docker/Dockerfile`** - Docker containerization
- **`.gitlab-ci.yml`** - CI/CD pipeline configuration

## Technology Stack
- **Frontend Framework**: React 19.0.0
- **Build Tool**: Vite
- **Styling**: Tailwind CSS 4.1.5
- **Routing**: React Router DOM 7.5.3
- **HTTP Client**: Axios 1.9.0
- **Animations**: Framer Motion 12.10.4
- **Icons**: React Icons 5.5.0
- **Notifications**: React Toastify 11.0.5
- **Phone Input**: React Phone Number Input 3.4.12

## Environment Variables Structure
```
VITE_ENV=development/production/local
VITE_API_URL=<main_api_url>
VITE_LOGIN_URL=<authentication_service_url>
VITE_FORGET_URL=<password_reset_service_url>
VITE_READINGEXAM_URL=<reading_exam_service_url>
VITE_PAYEMENT_URL=<payment_service_url>
VITE_MONITOR_URL=<monitoring_service_url>
VITE_READING_EVEALUTION_URL=<reading_evaluation_service_url>
```

## Service Architecture
The project follows a microservices architecture with separate services for:
- Authentication (Login/Register/Password Reset)
- Exam Modules (Reading, Writing, Speaking, Listening)
- Payment Processing
- User Monitoring
- Reading Evaluation

## Routing Structure
- **`/`** - Home page with Hero, About, Testimonials, Contact
- **`/signup`** - User registration
- **`/signin`** - User login
- **`/forgot-password`** - Password reset request
- **`/reset-password`** - Password reset form
- **`/dashboard`** - Protected dashboard with nested routes:
  - `/dashboard` - Overview
  - `/dashboard/speaking` - Speaking module
  - `/dashboard/writing` - Writing module
  - `/dashboard/reading` - Reading module
  - `/dashboard/listening` - Listening module
- **`/reading-instructions`** - Reading test instructions
- **`/reading-module`** - Reading test execution
- **`/logout`** - User logout

## Component Organization
- **Layout Components**: Navbar, Sidebar, Topbar for consistent UI structure
- **Field Components**: Reusable form inputs, buttons, date pickers
- **Page Components**: Organized by feature (auth, dashboard, home)
- **Service Components**: API integration and business logic
- **Common Components**: Shared utilities and constants

This structure provides a scalable, maintainable React application with clear separation of concerns and modular architecture.
