import Service from "../../../services/Service";

export const fetchAndTransformQuestions = async (type) => {
  const response = await Service.userReadingExamService({
    exam_type: type,
    fill_rw_responses: [],
    fill_reading_responses: [],
    mcq_single_responses: [],
    mcq_multiple_responses: [],
    reorder_paragraph_responses: [],
  });

  const transformedQuestions = [];

  // Add Fill in the Blanks (R&W) questions
  if (response.data.fill_rw_questions) {
    transformedQuestions.push(
      ...response.data.fill_rw_questions.map((q) => ({
        name: "Reading & Writing: Fill in the Blanks",
        description: "Select the appropriate words to complete the text",
        data: {
          id: q.id,
          paragraph: q.paragraph,
          options: Array.isArray(q.answers)
            ? q.answers.map((ans) => ans.options || [])
            : [],
          correct_answers: q.correct_answers || q.answers || [],
        },
      }))
    );
  }

  // Add Fill in the Blanks (Reading) questions
  if (response.data.fill_reading_questions) {
    transformedQuestions.push(
      ...response.data.fill_reading_questions.map((q) => ({
        name: "Reading: Fill in the Blanks",
        description: "Choose the correct words to complete the text passage",
        data: {
          id: q.id,
          paragraph: q.paragraph,
          options: q.word_bank || [],
          correct_answers: q.correct_answers || q.answers || [],
        },
      }))
    );
  }

  // Add Multiple Choice Single Answer questions
  if (response.data.mcq_single_questions) {
    transformedQuestions.push(
      ...response.data.mcq_single_questions.map((q) => ({
        name: "Multiple Choice, Single Answer",
        description: "Choose the best answer",
        data: {
          id: q.id,
          question_stem: q.question_stem,
          passage_for_question: q.passage_for_question,
          options: q.options || [],
          correct_answers: q.correct_answers || [q.answer],
          question_type: "choose_the_best",
          showPassage: true,
        },
      }))
    );
  }

  // Add Multiple Choice Multiple Answer questions
  if (type === "academic" && response.data.mcq_multiple_questions) {
    transformedQuestions.push(
      ...response.data.mcq_multiple_questions.map((q) => ({
        name: "Multiple Choice, Multiple Answers",
        description: "Select all correct answers that apply",
        data: {
          id: q.id,
          question: q.question_stem || q.question,
          passage: q.passage_for_question || q.passage,
          options: q.options || [],
          correct_answers: q.correct_answers || q.answers || [],
          question_type: q.question_type || "checkbox",
          showPassage: true,
        },
      }))
    );
  }

  // Add Reorder Paragraphs questions for academic
  if (type === "academic" && response.data.reorder_paragraph_questions) {
    transformedQuestions.push(
      ...response.data.reorder_paragraph_questions.map((q) => ({
        name: "Re-order Paragraphs",
        description: "Put the paragraphs in the correct order",
        data: {
          id: q.id,
          paragraphs: q.paragraphs || [],
          correct_answers: q.correct_order || [],
        },
      }))
    );
  }

  return transformedQuestions;
};