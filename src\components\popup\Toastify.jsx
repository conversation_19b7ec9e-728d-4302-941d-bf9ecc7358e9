import { useEffect } from "react";
import { Bounce, toast, ToastContainer } from "react-toastify";

const Toastify = ({ res, resClear }) => {
  useEffect(() => {
    if (!res) return;

    const { status, message, statusText, response, data } = res;

    const fallbackMessage = message || "Something went wrong!";
    const serverMessage =
      response?.data?.message ||
      response?.data ||
      data?.message ||
      data ||
      fallbackMessage;

    if (status >= 200 && status < 300) {
      toast.success(serverMessage || "Success");
    } else if (status >= 400 && status < 500) {
      toast.warn(serverMessage || `Client Error: ${statusText}`);
    } else if (status >= 500) {
      toast.error(serverMessage || `Server Error: ${statusText}`);
    } else {
      toast.info(fallbackMessage);
    }

    resClear();
  }, [res, resClear]);

  return (
    <ToastContainer
      position="top-right"
      autoClose={2000}
      hideProgressBar={false}
      closeOnClick={false}
      pauseOnFocusLoss
      pauseOnHover
      draggable
      theme="light"
      transition={Bounce}
    />
  );
};

export default Toastify;
