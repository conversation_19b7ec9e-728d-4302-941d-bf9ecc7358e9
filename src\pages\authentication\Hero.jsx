const Hero = () => {
  return (
    <div className="container mx-auto px-4 bg-[#f5f5f5] py-5">
      <div className="flex flex-col lg:flex-row justify-between items-center my-10 md:my-20 mx-2 md:mx-10 gap-8">
        {/* Left Side Content */}
        <div className="w-full lg:w-1/4 text-center lg:text-left">
          <blockquote className="text-[var(--primary-color)] text-3xl md:text-4xl">
            "
          </blockquote>
          <p className="mb-5 text-xl md:text-2xl">
            Expert's Remarkable Teaching Transformed Our Learning – Highly
            Recommended!
          </p>

          <div className="flex items-center gap-4 justify-center lg:justify-start">
            <div className="flex -space-x-2">
              <div className="w-8 h-8 rounded-full border-2 border-white bg-gray-300"></div>
              <div className="w-8 h-8 rounded-full border-2 border-white bg-gray-400"></div>
              <div className="w-8 h-8 rounded-full border-2 border-white bg-gray-500"></div>
            </div>
            <div>
              <div className="text-orange-500 font-semibold">
                150+ Reviews (4.9 of 5)
              </div>
              <div className="text-sm text-gray-500">
                Reviews from valued Students
              </div>
            </div>
          </div>
        </div>

        {/* Center Image */}
        <div className="relative w-full md:w-3/4 lg:w-2/4 flex justify-center my-8 lg:my-0">
          <div className="absolute w-[300px] md:w-[400px] h-[300px] md:h-[400px] rounded-full left-1/2 top-1/2 -translate-x-1/2 -translate-y-1/2"></div>
          <img
            src={hero}
            alt="PTE Expert"
            className="relative z-10 w-[300px] md:w-[400px] lg:w-[500px] h-auto"
          />
          <div className="absolute bottom-0 z-20 w-full flex justify-center lg:justify-start">
            <button className="bg-black text-white px-4 md:px-6 py-2 mb-10 md:mb-20 font-bold rounded-full flex items-center">
              Start Here{" "}
              <span className="text-[var(--primary-color)] text-xl md:text-2xl font-bold ml-2 md:ml-3">
                →
              </span>
            </button>
          </div>
        </div>

        {/* Right Side Tags */}
        <div className="w-full lg:w-1/4 flex flex-wrap gap-3 md:gap-6 justify-center lg:justify-end">
          <span className="bg-black text-white font-bold px-3 md:px-4 py-1 md:py-2 rounded-full text-sm md:text-base">
            +
          </span>
          <span className="bg-[var(--primary-color)] text-white px-3 md:px-4 py-1 md:py-2 rounded-full text-sm md:text-base">
            #Writing
          </span>
          <span className="bg-[var(--primary-color)] text-white px-3 md:px-4 py-1 md:py-2 rounded-full text-sm md:text-base">
            #Speaking
          </span>
          <span className="bg-[var(--secondary-color)] text-white px-3 md:px-4 py-1 md:py-2 rounded-full text-sm md:text-base">
            #Reading
          </span>
          <span className="bg-[var(--secondary-color)] text-white px-3 md:px-4 py-1 md:py-2 rounded-full text-sm md:text-base">
            #Listening
          </span>
          <span className="bg-[var(--primary-color)] text-white px-3 md:px-4 py-1 md:py-2 rounded-full text-sm md:text-base">
            #Core
          </span>
          <span className="bg-[var(--secondary-color)] text-white px-3 md:px-4 py-1 md:py-2 rounded-full text-sm md:text-base">
            #Academic
          </span>
        </div>
      </div>
    </div>
  );
};