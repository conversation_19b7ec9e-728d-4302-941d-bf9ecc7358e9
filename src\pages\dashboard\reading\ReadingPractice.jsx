import { useLocation, useNavigate } from "react-router-dom";
import Button from "../../../components/field/Button";

const ReadingPractice = () => {
  const location = useLocation();
  const navigate = useNavigate();
  const { questionData, allQuestions, currentIndex, examType } =
    location.state || {};

  if (!questionData || !allQuestions) {
    return navigate("/dashboard/reading");
  }

  const handleNavigation = (direction) => {
    const newIndex = direction === "next" ? currentIndex + 1 : currentIndex - 1;

    if (newIndex >= 0 && newIndex < allQuestions.length) {
      navigate("/reading", {
        state: {
          questionData: allQuestions[newIndex],
          allQuestions,
          currentIndex: newIndex,
          examType,
        },
        replace: true,
      });
    }
  };

  return (
    <div className="min-h-screen bg-gray-50 p-6">
      <div className="max-w-4xl mx-auto">
        <div className="flex justify-between items-center mb-8">
          <div>
            <div className="flex items-center gap-4">
              <h1 className="text-2xl font-bold">{questionData.name}</h1>
              <span className="bg-[var(--primary-color)] text-white px-3 py-1 rounded-full text-sm">
                {examType.toUpperCase()}
              </span>
            </div>
            <p className="text-gray-500 mt-2">
              Question {currentIndex + 1} of {allQuestions.length}
            </p>
          </div>
          <Button
            name="Exit Practice"
            onClick={() => navigate("/dashboard/reading")}
            className="bg-red-500 text-white px-4 py-2 rounded-lg"
          />
        </div>

        {/* Question content */}
        <div className="bg-white rounded-xl p-6 shadow-sm">
          <div className="mb-6">
            <h2 className="text-lg font-semibold mb-4">Instructions</h2>
            <p className="text-gray-600">{questionData.description}</p>
          </div>

          <div className="space-y-6">
            {questionData.data.passage && (
              <div className="bg-gray-50 p-4 rounded-lg">
                <p className="text-gray-800">{questionData.data.passage}</p>
              </div>
            )}

            {questionData.data.options && (
              <div className="space-y-3">
                {questionData.data.options.map((option, index) => (
                  <div
                    key={index}
                    className="flex items-center space-x-3 p-3 border rounded-lg hover:bg-gray-50"
                  >
                    <input
                      type={
                        questionData.name.includes("Multiple Answers")
                          ? "checkbox"
                          : "radio"
                      }
                      name="answer"
                      id={`option-${index}`}
                      className="h-4 w-4 text-[var(--primary-color)]"
                    />
                    <label htmlFor={`option-${index}`} className="flex-1">
                      {option}
                    </label>
                  </div>
                ))}
              </div>
            )}

            {questionData.data.answers && (
              <div className="space-y-4">
                <p className="font-medium">Fill in the blanks:</p>
                <div className="flex flex-wrap gap-3">
                  {questionData.data.answers.map((answer, index) => (
                    <div
                      key={index}
                      className="bg-gray-100 px-4 py-2 rounded-lg cursor-pointer hover:bg-gray-200"
                    >
                      {answer}
                    </div>
                  ))}
                </div>
              </div>
            )}
          </div>

          <div className="mt-8 flex justify-between items-center">
            <Button
              name="Previous"
              onClick={() => handleNavigation("prev")}
              disabled={currentIndex === 0}
              className={`px-6 py-2 rounded-lg ${
                currentIndex === 0
                  ? "bg-gray-200 text-gray-500"
                  : "bg-[var(--primary-color)] text-white"
              }`}
            />
            <Button
              name="Submit Answer"
              className="bg-[var(--primary-color)] text-white px-6 py-2 rounded-lg"
            />
            <Button
              name="Next"
              onClick={() => handleNavigation("next")}
              disabled={currentIndex === allQuestions.length - 1}
              className={`px-6 py-2 rounded-lg ${
                currentIndex === allQuestions.length - 1
                  ? "bg-gray-200 text-gray-500"
                  : "bg-[var(--primary-color)] text-white"
              }`}
            />
          </div>
        </div>
      </div>
    </div>
  );
};

export default ReadingPractice;
