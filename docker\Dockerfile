# Stage 1: Build React App
FROM node:18 as build

WORKDIR /app

# Copy only dependency files first to leverage Docker layer caching
COPY package.json package-lock.json ./

# Clean up node_modules and lock file before fresh install
RUN rm -rf node_modules package-lock.json && npm install

# Copy the rest of the application
COPY . .

# Set environment variables
ARG VITE_API_URL
ARG VITE_ENV
ENV VITE_API_URL=$VITE_API_URL
ENV VITE_ENV=$VITE_ENV

EXPOSE 5173

CMD ["npm", "run", "dev"]
