import {
  RiSpeakLine,
  RiBookReadLine,
  RiHeadphoneLine,
  RiFileList3Line,
  RiBarChartLine,
  RiTimeLine,
  RiAwardLine, // Changed from RiMedalLine
  RiCalendarLine,
  RiFlag2Line, // Changed from RiTargetLine
} from "react-icons/ri";
import Button from "../../components/field/Button";
import React, { useState } from "react";
import { PROJECT_NAME } from "../../components/common/Name";
const Overview = () => {
  const [examType, setExamType] = useState("academic");

  const stats = {
    academic: [
      {
        icon: RiSpeakLine,
        name: "Speaking",
        score: "--/90",
        attempts: 0,
        target: 79,
      },
      {
        icon: RiFileList3Line,
        name: "Writing",
        score: "--/90",
        attempts: 0,
        target: 79,
      },
      {
        icon: RiBookReadLine,
        name: "<PERSON>",
        score: "--/90",
        attempts: 0,
        target: 79,
      },
      {
        icon: RiHeadphoneLine,
        name: "Listening",
        score: "--/90",
        attempts: 0,
        target: 79,
      },
    ],
    core: [
      {
        icon: RiSpeakLine,
        name: "Speaking",
        score: "--/90",
        attempts: 0,
        target: 65,
      },
      {
        icon: RiFileList3Line,
        name: "Writing",
        score: "--/90",
        attempts: 0,
        target: 65,
      },
      {
        icon: RiBookReadLine,
        name: "Reading",
        score: "--/90",
        attempts: 0,
        target: 65,
      },
      {
        icon: RiHeadphoneLine,
        name: "Listening",
        score: "--/90",
        attempts: 0,
        target: 65,
      },
    ],
  };

  return (
    <div className="space-y-8 max-w-[1920px] mx-auto">
      {/* Hero Section */}
      <div className="bg-gradient-to-r from-[var(--primary-color)] to-[var(--primary-color)] rounded-2xl p-8 text-white">
        <div className="max-w-3xl">
          <h1 className="text-3xl font-bold mb-4">
            Welcome to {PROJECT_NAME} Practice Hub
          </h1>
          <p className="text-lg opacity-90 mb-6">
            Prepare for success with our comprehensive PTE practice platform
          </p>
          <div className="flex gap-4">
            <button className="bg-white text-[var(--primary-color)] px-6 py-3 rounded-lg font-medium hover:bg-opacity-90 transition-all">
              Start Practice Test
            </button>
            <button className="bg-white/20 text-white px-6 py-3 rounded-lg font-medium hover:bg-white/30 transition-all">
              View Study Plan
            </button>
          </div>
        </div>
      </div>

      {/* Stats Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mt-8">
        {stats[examType].map((stat, index) => (
          <div
            key={index}
            className="bg-white p-6 rounded-xl shadow-sm hover:shadow-md transition-all"
          >
            <div className="flex items-center gap-4">
              <div className="w-12 h-12 bg-[var(--primary-color)] bg-opacity-10 rounded-lg flex items-center justify-center">
                <stat.icon className="text-2xl text-[var(--primary-color)]" />
              </div>
              <div>
                <p className="text-gray-500">{stat.name}</p>
                <p className="text-2xl font-bold">{stat.score}</p>
                <div className="flex items-center gap-2 text-sm">
                  <span className="text-gray-400">{stat.attempts} Tests</span>
                  <span className="text-[var(--primary-color)]">
                    Target: {stat.target}
                  </span>
                </div>
              </div>
            </div>
          </div>
        ))}
      </div>

      {/* Additional Features Grid */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
        {/* Recent Activity */}
        <div className="bg-white p-6 rounded-xl shadow-sm">
          <div className="flex items-center gap-3 mb-4">
            <RiBarChartLine className="text-2xl text-[var(--primary-color)]" />
            <h2 className="text-lg font-semibold">Recent Activity</h2>
          </div>
          <div className="space-y-3">
            <div className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
              <div>
                <p className="font-medium">Speaking Practice</p>
                <p className="text-sm text-gray-500">Score: 75/90</p>
              </div>
              <span className="text-xs text-gray-400">2 hours ago</span>
            </div>
            <div className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
              <div>
                <p className="font-medium">Reading Test</p>
                <p className="text-sm text-gray-500">Score: 82/90</p>
              </div>
              <span className="text-xs text-gray-400">Yesterday</span>
            </div>
          </div>
        </div>

        {/* Performance Trends */}
        <div className="bg-white p-6 rounded-xl shadow-sm">
          <div className="flex items-center gap-3 mb-4">
            <RiTimeLine className="text-2xl text-[var(--primary-color)]" />
            <h2 className="text-lg font-semibold">Performance Trends</h2>
          </div>
          <div className="space-y-3">
            <div className="flex flex-col gap-2">
              <div className="flex items-center justify-between">
                <span className="text-sm">Speaking</span>
                <span className="text-sm text-green-500">↑ 12%</span>
              </div>
              <div className="flex items-center justify-between">
                <span className="text-sm">Writing</span>
                <span className="text-sm text-red-500">↓ 5%</span>
              </div>
              <div className="flex items-center justify-between">
                <span className="text-sm">Reading</span>
                <span className="text-sm text-green-500">↑ 8%</span>
              </div>
              <div className="flex items-center justify-between">
                <span className="text-sm">Listening</span>
                <span className="text-sm text-green-500">↑ 15%</span>
              </div>
            </div>
          </div>
        </div>

        {/* Practice Summary */}
        <div className="bg-white p-6 rounded-xl shadow-sm">
          <div className="flex items-center gap-3 mb-4">
            <RiFileList3Line className="text-2xl text-[var(--primary-color)]" />
            <h2 className="text-lg font-semibold">Practice Summary</h2>
          </div>
          <div className="space-y-4">
            <div className="grid grid-cols-2 gap-4">
              <div className="text-center p-3 bg-gray-50 rounded-lg">
                <p className="text-2xl font-bold text-[var(--primary-color)]">
                  24
                </p>
                <p className="text-sm text-gray-500">Total Tests</p>
              </div>
              <div className="text-center p-3 bg-gray-50 rounded-lg">
                <p className="text-2xl font-bold text-[var(--primary-color)]">
                  78
                </p>
                <p className="text-sm text-gray-500">Avg Score</p>
              </div>
            </div>
            <div className="text-center">
              <button className="w-full bg-gray-50 text-[var(--primary-color)] text-sm font-medium py-2 rounded-lg hover:bg-gray-100 transition-all">
                View Detailed Analysis
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default Overview;
