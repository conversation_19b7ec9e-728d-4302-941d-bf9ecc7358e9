import DatePicker from "react-datepicker";
import "react-datepicker/dist/react-datepicker.css";

import PropTypes from "prop-types";

export default function DatePickerComponent({
  label,
  required,
  disabled,
  selected,
  onChange,
  dateFormat,
  value,
  maxDate,
  minDate,
  showMonthYearPicker,
  showYearPicker,
  showTimeSelect,
  showTimeSelectOnly,
  placeholderText,
  timeCaption,
  timeFormat,
}) {
  return (
    <div className="flex flex-col w-full h-full">
      {label && (
        <label
          htmlFor={label}
          className="tracking-wide block mb-1 font-semibold"
        >
          {label}
          {required && (
            <span id="required" className="text-lg font-bold text-red-600 ml-1">
              *
            </span>
          )}
        </label>
      )}
      <DatePicker
        disabled={disabled}
        id={label}
        className={`w-full py-3 text-black px-4 border-2 border-gray-300 rounded-md focus:ring-gray-500 focus:border-gray-500 outline-none placeholder:text-gray-500 ${
          disabled ? "bg-gray-400" : ""
        }`}
        selected={selected}
        onChange={onChange}
        dateFormat={dateFormat}
        value={value}
        maxDate={maxDate}
        minDate={minDate}
        showMonthYearPicker={showMonthYearPicker}
        showYearPicker={showYearPicker}
        showTimeSelect={showTimeSelect}
        showTimeSelectOnly={showTimeSelectOnly}
        timeIntervals={5}
        timeCaption={timeCaption}
        timeFormat={timeFormat}
        placeholderText={placeholderText}
        autoComplete="off"
      />
    </div>
  );
}

DatePickerComponent.propTypes = {
  label: PropTypes.string,
  required: PropTypes.bool,
  disabled: PropTypes.bool,
  selected: PropTypes.instanceOf(Date),
  onChange: PropTypes.func.isRequired,
  dateFormat: PropTypes.string,
  value: PropTypes.oneOfType([PropTypes.string, PropTypes.instanceOf(Date)]),
  maxDate: PropTypes.instanceOf(Date),
  minDate: PropTypes.instanceOf(Date),
  showMonthYearPicker: PropTypes.bool,
  showYearPicker: PropTypes.bool,
  showTimeSelect: PropTypes.bool,
  showTimeSelectOnly: PropTypes.bool,
  timeCaption: PropTypes.string,
  timeFormat: PropTypes.string,
  placeholderText: PropTypes.string,
};
