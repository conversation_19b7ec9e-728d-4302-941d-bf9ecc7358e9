import { useState, useEffect } from "react";
import {
  RiNotification3Line,
  RiSearchLine,
  RiLogoutCircleLine,
  RiSettings4Line,
  RiUserLine,
} from "react-icons/ri";
import { useNavigate } from "react-router-dom";

const Topbar = () => {
  const navigate = useNavigate();
  const [userData, setUserData] = useState({
    userId: "",
    email: "",
  });
  const [showProfileMenu, setShowProfileMenu] = useState(false);

  useEffect(() => {
    const userId = sessionStorage.getItem("user_id");
    const email = sessionStorage.getItem("user_email");
    setUserData({ userId, email });
  }, []);

  const getInitial = () => {
    if (userData.email) {
      return userData.email.charAt(0).toUpperCase();
    }
    return "U";
  };

  const handleLogout = () => {
    navigate("/logout");
  };

  return (
    <div className="h-16 bg-white shadow-sm flex items-center justify-between px-6 sticky top-0 z-40">
      <div className="flex items-center gap-4">
        <div className="relative">
          <RiSearchLine className="absolute left-3 top-1/2 -translate-y-1/2 text-gray-400" />
          <input
            type="text"
            placeholder="Search..."
            className="pl-10 pr-4 py-2 rounded-lg border border-gray-200 focus:outline-none focus:border-[var(--primary-color)] w-64"
          />
        </div>
      </div>

      <div className="flex items-center gap-6">
        {/* Notification Bell with Counter */}
        <button className="relative p-2 hover:bg-gray-100 rounded-full group">
          <RiNotification3Line className="text-xl text-gray-600 group-hover:text-[var(--primary-color)]" />
          <span className="absolute -top-1 -right-1 w-5 h-5 bg-red-500 rounded-full text-white text-xs flex items-center justify-center">
            3
          </span>
        </button>

        {/* Profile Section */}
        <div className="relative">
          <div
            className="flex items-center gap-3 cursor-pointer p-2 rounded-lg hover:bg-gray-50 transition-all"
            onClick={() => setShowProfileMenu(!showProfileMenu)}
          >
            <div className="w-10 h-10 bg-[var(--primary-color)] rounded-full flex items-center justify-center shadow-lg hover:shadow-xl transition-all">
              <span className="text-white font-medium">{getInitial()}</span>
            </div>
            <div>
              <p className="text-sm text-gray-500">Welcome back,</p>
              <p className="font-medium text-gray-700">
                {userData.email || "Loading..."}
              </p>
            </div>
          </div>

          {/* Profile Dropdown Menu */}
          {showProfileMenu && (
            <div className="absolute right-0 top-full mt-2 w-60 bg-white rounded-lg shadow-lg border border-gray-100 py-2 z-50">
              <div className="px-4 py-2 border-b border-gray-100">
                <p className="text-sm text-gray-500">Signed in as</p>
                <p className="font-medium text-gray-700 truncate">
                  {userData.email}
                </p>
              </div>

              <button
                className="w-full px-4 py-2 text-left hover:bg-gray-50 flex items-center gap-2 text-gray-700"
                onClick={() => navigate("/profile")}
              >
                <RiUserLine className="text-gray-500" />
                <span>Your Profile</span>
              </button>

              <button
                className="w-full px-4 py-2 text-left hover:bg-gray-50 flex items-center gap-2 text-gray-700"
                onClick={() => navigate("/settings")}
              >
                <RiSettings4Line className="text-gray-500" />
                <span>Settings</span>
              </button>

              <div className="border-t border-gray-100 mt-2">
                <button
                  className="w-full px-4 py-2 text-left hover:bg-gray-50 flex items-center gap-2 text-red-600"
                  onClick={handleLogout}
                >
                  <RiLogoutCircleLine />
                  <span className="cursor-pointer">Sign Out</span>
                </button>
              </div>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default Topbar;
