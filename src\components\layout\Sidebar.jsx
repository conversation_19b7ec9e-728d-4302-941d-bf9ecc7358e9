import { useState } from "react";
import { Link, useLocation, useNavigate } from "react-router-dom";
import {
  RiSpeakLine,
  RiBookReadLine,
  RiHeadphoneLine,
  RiDashboardLine,
  RiFileList3Line,
  RiLogoutBoxRLine,
} from "react-icons/ri";
import { PROJECT_NAME } from "../common/Name";

const Sidebar = () => {
  const location = useLocation();
  const navigate = useNavigate();
  const [isOpen, setIsOpen] = useState(true);

  const menuItems = [
    { path: "/dashboard", icon: RiDashboardLine, name: "Overview" },
    { path: "/dashboard/speaking", icon: RiSpeakLine, name: "Speaking" },
    { path: "/dashboard/writing", icon: RiFileList3Line, name: "Writing" },
    { path: "/reading-instructions", icon: RiBookReadLine, name: "Reading" },
    { path: "/dashboard/listening", icon: RiH<PERSON>phoneL<PERSON>, name: "Listening" },
  ];

  return (
    <div
      className={`fixed top-0 left-0 h-screen bg-white shadow-lg z-50 ${
        isOpen ? "w-64" : "w-20"
      } transition-all duration-300 flex flex-col`}
    >
      <div className="p-4 flex-1 flex flex-col h-full overflow-hidden">
        <div className="flex items-center gap-2 mb-8 flex-shrink-0">
          <div className="w-10 h-10 bg-[var(--primary-color)] rounded-full flex items-center justify-center">
            <span className="text-white font-bold text-xl">P</span>
          </div>
          {isOpen && <span className="text-xl font-bold">{PROJECT_NAME} Dashboard</span>}
        </div>

        <nav className="flex-1 overflow-y-auto scrollbar-thin scrollbar-thumb-gray-200 scrollbar-track-transparent">
          <div className="space-y-2">
            {menuItems.map((item) => (
              <Link
                key={item.path}
                to={item.path}
                className={`flex items-center gap-4 p-3 rounded-lg transition-all ${
                  location.pathname === item.path
                    ? "bg-[var(--primary-color)] text-white"
                    : "hover:bg-gray-100"
                }`}
              >
                <item.icon className="text-2xl" />
                {isOpen && <span>{item.name}</span>}
              </Link>
            ))}
          </div>
        </nav>

        <button
          className="flex items-center gap-4 p-3 rounded-lg w-full text-red-500 hover:bg-red-50 mt-4 flex-shrink-0"
          onClick={() => navigate("/logout")}
        >
          <RiLogoutBoxRLine className="text-2xl" />
          {isOpen && <span className="cursor-pointer">Logout</span>}
        </button>
      </div>
    </div>
  );
};

export default Sidebar;
