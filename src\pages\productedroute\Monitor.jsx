import { useState, useRef, useEffect } from "react";
import Service from "../../services/Service";

const Monitor = () => {
  const videoRef = useRef(null);
  const [res, setRes] = useState(null);
  const userId = sessionStorage.getItem("user_id");
  const [intervalId, setIntervalId] = useState(null);

  useEffect(() => {
    startMonitoring();
  }, []);

  const startMonitoring = async () => {
    try {
      console.log("Starting monitoring for User ID:", userId);
      const response = await Service.userStartMonitorService({ userId });
      setRes(response.data);

      startVideoMonitoring();
    } catch (error) {
      setRes(error);
    }
  };

  const startVideoMonitoring = async () => {
    if (navigator.mediaDevices && navigator.mediaDevices.getUserMedia) {
      try {
        const stream = await navigator.mediaDevices.getUserMedia({
          video: true,
        });
        if (videoRef.current) {
          videoRef.current.srcObject = stream;
        }
        const interval = setInterval(sendFrame, 1000);
        setIntervalId(interval);
      } catch (error) {
        setRes(
          "Failed to access webcam. Please check your camera permissions."
        );
      }
    } else {
      setRes("Camera is not available on this device.");
    }
  };

  const sendFrame = async () => {
    const canvas = document.createElement("canvas");
    const context = canvas.getContext("2d");
    const video = videoRef.current;

    if (video && video.videoWidth && video.videoHeight) {
      canvas.width = video.videoWidth;
      canvas.height = video.videoHeight;
      context.drawImage(video, 0, 0, canvas.width, canvas.height);
      const frame = canvas.toDataURL("image/jpeg");

      try {
        await Service.userProcessFrameMonitorService({ frame }); // Updated API call
      } catch (error) {
        setRes("Error processing video frame.");
      }
    }
  };

  const stopMonitoring = async () => {
    try {
      await Service.userStopMonitorService({ userId }); // Updated API call

      if (intervalId) {
        clearInterval(intervalId);
        setIntervalId(null);
      }

      if (videoRef.current && videoRef.current.srcObject) {
        const stream = videoRef.current.srcObject;
        const tracks = stream.getTracks();
        tracks.forEach((track) => track.stop());
        videoRef.current.srcObject = null;
      }
    } catch (error) {
      console.error("Error stopping monitoring:", error);
      setRes("Error stopping monitoring. Please try again.");
    }
  };

  return (
    <div className="fixed bottom-0 right-4 z-50">
      <video
        ref={videoRef}
        autoPlay
        muted
        className="w-[150px] h-[115px] rounded-lg border-2 border-gray-300 shadow-lg bg-black"
      />
    </div>
  );
};

export default Monitor;
