import axios from "axios";
import config from "../../config";

export const monitor = {
  userStartMonitorService: (data) => {
    return axios.post(`${config.MONITOR_URL}/start_monitoring`, data);
  },
  userProcessFrameMonitorService: (data) => {
    return axios.post(`${config.MONITOR_URL}/process_frame`, data);
  },
  userStopMonitorService: (data) => {
    return axios.post(`${config.MONITOR_URL}/stop_monitoring`, data);
  },
};
