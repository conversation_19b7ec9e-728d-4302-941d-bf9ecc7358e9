import React, { useState, useEffect, useCallback } from "react";
import Toastify from "../../../components/popup/Toastify";
import { handleSubmit } from "./ReadingEvaluation";
import { useNavigate } from "react-router-dom";
import QuestionRenderer from "./ReadingQuestionRenderer";
import { fetchAndTransformQuestions } from "./ReadingQuestionService";
import { motion } from "framer-motion";
import {
  FaGraduationCap,
  FaBook,
  FaArrowRight,
  FaChalkboardTeacher,
  FaUserGraduate,
  FaBookReader,
  FaArrowLeft,
  FaClock,
} from "react-icons/fa";
import Monitor from "../../productedroute/Monitor";
import Service from "../../../services/Service";

const Reading = () => {
  const navigate = useNavigate();
  const [loading, setLoading] = useState(false);
  const [res, setRes] = useState(null);
  const [examType, setExamType] = useState(null);
  const [questions, setQuestions] = useState({
    academic: [],
    core: [],
  });
  const [timeLeft, setTimeLeft] = useState(30 * 60);
  const [isTimerRunning, setIsTimerRunning] = useState(true);
  const [currentQuestionIndex, setCurrentQuestionIndex] = useState(0);
  const [answers, setAnswers] = useState({});
  const [draggedItem, setDraggedItem] = useState(null);
  const [paragraphOrder, setParagraphOrder] = useState([]);
  const [isMonitoring, setIsMonitoring] = useState(false);
  const [isExamStarted, setIsExamStarted] = useState(false);
  const [isTimerActive, setIsTimerActive] = useState(false);
  const [hasAttemptedExit, setHasAttemptedExit] = useState(false);
  const [scoreResponse, setScoreResponse] = useState(null);
  const [error, setError] = useState(null);
  const [isSubmitting, setIsSubmitting] = useState(false);

  const handleExamSubmit = async () => {
    try {
      setIsSubmitting(true);
      setIsTimerRunning(false);
      const result = await handleSubmit(examType, questions[examType], answers);
      if (result.error) {
        setError(result.error);
      } else {
        setScoreResponse(result.data);
      }
      setExamType(null);
    } catch (error) {
      console.error("Error submitting exam:", error);
      setError("Failed to submit exam. Please try again.");
    } finally {
      setIsSubmitting(false);
    }
  };

  const stopMonitoring = async () => {
    try {
      const userId = sessionStorage.getItem("user_id");
      await Service.userStopMonitorService({ userId });
      setIsMonitoring(false);
    } catch (error) {
      console.error("Error stopping monitoring:", error);
    }
  };

  useEffect(() => {
    if (isExamStarted) {
      const handlePopState = (e) => {
        e.preventDefault();
        setHasAttemptedExit(true);
        handleExamSubmit();
      };

      const handleVisibilityChange = () => {
        if (document.hidden && isExamStarted) {
          setHasAttemptedExit(true);
          handleExamSubmit();
        }
      };

      const handleBeforeUnload = (e) => {
        if (isExamStarted) {
          e.preventDefault();
          e.returnValue = "";
        }
      };

      window.addEventListener("popstate", handlePopState);
      document.addEventListener("visibilitychange", handleVisibilityChange);
      window.addEventListener("beforeunload", handleBeforeUnload);

      return () => {
        window.removeEventListener("popstate", handlePopState);
        document.removeEventListener(
          "visibilitychange",
          handleVisibilityChange
        );
        window.removeEventListener("beforeunload", handleBeforeUnload);
      };
    }
  }, [isExamStarted]);

  const handleExamTypeSelect = async (type) => {
    setExamType(type);
    setIsMonitoring(true);
    setAnswers({}); // Reset answers
    setParagraphOrder([]); // Reset paragraph order
    try {
      setLoading(true);
      const transformedQuestions = await fetchAndTransformQuestions(type);
      setQuestions((prev) => ({
        ...prev,
        [type]: transformedQuestions,
      }));
      setIsExamStarted(true);
      setIsTimerRunning(true);
    } catch (error) {
      setRes({ type: "error", message: "Error fetching questions" });
      console.error("Error fetching questions:", error);
    } finally {
      setLoading(false);
    }
  };

  const startExam = () => {
    setIsTimerActive(true);
    setIsTimerRunning(true);
  };

  const saveCurrentAnswer = useCallback(() => {
    const currentQuestion = questions[examType]?.[currentQuestionIndex];
    if (currentQuestion) {
      const questionId = currentQuestion.data.id;
      const currentAnswer = answers[questionId];
      if (currentAnswer !== undefined && currentAnswer !== null) {
        handleAnswerSave(questionId, currentAnswer);
        console.log(
          `Saved answer before navigation for ${questionId}:`,
          currentAnswer
        );
      }
    }
  }, [questions, examType, currentQuestionIndex, answers]);

  const handleNext = () => {
    if (
      questions[examType] &&
      currentQuestionIndex < questions[examType].length - 1
    ) {
      saveCurrentAnswer();
      setCurrentQuestionIndex((prev) => prev + 1);
    }
  };

  const handlePrevious = () => {
    if (currentQuestionIndex > 0) {
      saveCurrentAnswer();
      setCurrentQuestionIndex((prev) => prev - 1);
    }
  };

  const handleAnswerSave = useCallback((questionId, answer) => {
    setAnswers((prev) => {
      const newAnswers = {
        ...prev,
        [questionId]: answer,
      };
      console.log(`Updated answers for ${questionId}:`, newAnswers[questionId]);
      return newAnswers;
    });
  }, []);

  const handleDragStart = (index) => {
    setDraggedItem(index);
  };

  const handleDragOver = (e) => {
    e.preventDefault();
  };

  const handleDrop = (dropIndex) => {
    if (draggedItem === null) return;

    const currentQuestion = questions[examType][currentQuestionIndex];
    const newOrder = [...paragraphOrder];
    const [removed] = newOrder.splice(draggedItem, 1);
    newOrder.splice(dropIndex, 0, removed);

    setParagraphOrder(newOrder);
    handleAnswerSave(currentQuestion.data.id, newOrder);
    setDraggedItem(null);
  };

  useEffect(() => {
    let timer;
    if (isTimerRunning && timeLeft > 0 && !loading) {
      timer = setInterval(() => {
        setTimeLeft((prev) => prev - 1);
      }, 1000);
    } else if (timeLeft === 0) {
      setRes({ type: "error", message: "Time's up!" });
      handleExamSubmit();
    }
    return () => clearInterval(timer);
  }, [timeLeft, isTimerRunning, loading]);

  return (
    <div className="min-h-[calc(100vh-64px)] bg-gradient-to-br from-gray-50 via-white to-gray-100 p-6">
      <Toastify res={res} resClear={() => setRes(null)} />

      {!examType ? (
        <motion.div
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          className="max-w-6xl mx-auto relative"
        >
          <motion.button
            initial={{ opacity: 0, x: -20 }}
            animate={{ opacity: 1, x: 0 }}
            onClick={() => navigate("/reading-instructions")}
            className="absolute left-0 -top-2 cursor-pointer flex items-center gap-2 text-[var(--primary-color)] hover:text-[var(--primary-color)]/80 transition-colors"
          >
            <FaArrowLeft className="text-lg" />
            <span className="font-medium">Back to Instructions</span>
          </motion.button>
          <motion.div
            initial={{ y: -20 }}
            animate={{ y: 0 }}
            className="text-center mb-16"
          >
            <span className="inline-flex items-center gap-2 px-4 py-2 bg-blue-50 text-[var(--primary-color)] rounded-full text-sm font-medium mb-4">
              <FaBookReader className="text-lg" />
              PTE Reading Section
            </span>
            <h1 className="text-4xl font-bold text-[var(--primary-color)] mb-4">
              Reading Practice
            </h1>
            <p className="text-gray-600 text-lg">
              Enhance your reading skills with our comprehensive practice
              modules
            </p>
          </motion.div>

          <div className="grid md:grid-cols-2 gap-8 max-w-4xl mx-auto">
            <motion.div
              whileHover={{ y: -8, scale: 1.02 }}
              className="group relative"
            >
              <div className="absolute inset-0 bg-gradient-to-r from-[var(--primary-color)] to-[var(--primary-color)] rounded-2xl blur opacity-20 group-hover:opacity-30 transition-all" />
              <button
                onClick={() => handleExamTypeSelect("academic")}
                disabled={loading || examType !== null}
                className="relative w-full cursor-pointer bg-white p-8 rounded-2xl shadow-xl hover:shadow-2xl transition-all border border-gray-100"
              >
                <div className="absolute top-0 right-0 bg-gradient-to-r from-[var(--primary-color)] to-[var(--primary-color)] text-white px-4 py-2 rounded-bl-2xl rounded-tr-2xl text-sm font-medium">
                  <FaGraduationCap className="inline-block mr-2" />
                  Advanced
                </div>
                <div className="flex flex-col items-start text-left">
                  <div className="text-4xl mb-6 text-[var(--primary-color)]">
                    <FaChalkboardTeacher />
                  </div>
                  <h3 className="text-2xl font-bold text-gray-800 mb-4">
                    Academic
                  </h3>
                  <ul className="space-y-3 text-gray-600 mb-6">
                    <li className="flex items-center gap-2">
                      <FaUserGraduate className="text-[var(--primary-color)]" />
                      Comprehensive exam preparation
                    </li>
                    <li className="flex items-center gap-2">
                      <FaUserGraduate className="text-[var(--primary-color)]" />
                      Advanced level questions
                    </li>
                    <li className="flex items-center gap-2">
                      <FaUserGraduate className="text-[var(--primary-color)]" />
                      Detailed performance analysis
                    </li>
                  </ul>
                  <div className="flex items-center text-[var(--primary-color)] font-medium group-hover:text-[var(--primary-color)] transition-colors">
                    Start Practice
                    <FaArrowRight className="ml-2 group-hover:translate-x-1 transition-transform" />
                  </div>
                </div>
              </button>
            </motion.div>

            <motion.div
              whileHover={{ y: -8, scale: 1.02 }}
              className="group relative"
            >
              <div className="absolute inset-0 bg-gradient-to-r from-[var(--primary-color)] to-[var(--primary-color)] rounded-2xl blur opacity-20 group-hover:opacity-30 transition-all" />
              <button
                onClick={() => handleExamTypeSelect("core")}
                disabled={loading || examType !== null}
                className="relative cursor-pointer w-full bg-white p-8 rounded-2xl shadow-xl hover:shadow-2xl transition-all border border-gray-100"
              >
                <div className="absolute top-0 right-0 bg-gradient-to-r from-[var(--primary-color)] to-[var(--primary-color)] text-white px-4 py-2 rounded-bl-2xl rounded-tr-2xl text-sm font-medium">
                  <FaBook className="inline-block mr-2" />
                  Essential
                </div>
                <div className="flex flex-col items-start text-left">
                  <div className="text-4xl mb-6 text-[var(--primary-color)]">
                    <FaBookReader />
                  </div>
                  <h3 className="text-2xl font-bold text-gray-800 mb-4">
                    Core
                  </h3>
                  <ul className="space-y-3 text-gray-600 mb-6">
                    <li className="flex items-center gap-2">
                      <FaBook className="text-[var(--primary-color)]" />
                      Fundamental concepts
                    </li>
                    <li className="flex items-center gap-2">
                      <FaBook className="text-[var(--primary-color)]" />
                      Progressive difficulty
                    </li>
                    <li className="flex items-center gap-2">
                      <FaBook className="text-[var(--primary-color)]" />
                      Basic skill development
                    </li>
                  </ul>
                  <div className="flex items-center text-[var(--primary-color)] font-medium group-hover:text-[var(--primary-color)] transition-colors">
                    Start Practice
                    <FaArrowRight className="ml-2 group-hover:translate-x-1 transition-transform" />
                  </div>
                </div>
              </button>
            </motion.div>
          </div>
        </motion.div>
      ) : loading ? (
        <div className="flex flex-col justify-center items-center min-h-[400px] gap-8">
          <motion.div className="relative w-40 h-40">
            {[0, 1, 2, 3].map((index) => (
              <motion.div
                key={index}
                className="absolute inset-0 border-4 border-[var(--primary-color)]"
                initial={{ opacity: 0.3, rotate: 0 }}
                animate={{
                  opacity: [0.3, 1, 0.3],
                  rotate: 360,
                  scale: [1, 1.2, 1],
                  borderRadius: ["20%", "50%", "20%"],
                }}
                transition={{
                  duration: 3,
                  repeat: Infinity,
                  delay: index * 0.4,
                  ease: "easeInOut",
                }}
              />
            ))}
            <motion.div
              className="absolute inset-8 flex items-center justify-center"
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              transition={{ duration: 0.5 }}
            >
              <motion.div
                className="text-[var(--primary-color)] text-xl font-bold"
                animate={{ scale: [1, 1.2, 1] }}
                transition={{ duration: 2, repeat: Infinity }}
              >
                PTE
              </motion.div>
            </motion.div>
          </motion.div>

          <motion.div
            className="flex flex-col items-center gap-3"
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.5 }}
          >
            <div className="text-lg font-medium text-gray-600">
              Preparing your {examType} questions
            </div>
            <motion.div
              className="flex gap-2"
              animate={{ scale: [1, 1.1, 1] }}
              transition={{ duration: 1, repeat: Infinity }}
            >
              {["Reading"].map((skill, index) => (
                <motion.span
                  key={skill}
                  className="text-sm font-bold text-[var(--primary-color)]"
                  initial={{ opacity: 0 }}
                  animate={{ opacity: 1 }}
                  transition={{ delay: index * 0.5, duration: 0.5 }}
                >
                  {skill}
                </motion.span>
              ))}
            </motion.div>

            <motion.div className="w-64 h-2 bg-gray-200 rounded-full mt-4 overflow-hidden">
              <motion.div
                className="h-full bg-[var(--primary-color)]"
                initial={{ width: "0%" }}
                animate={{ width: "100%" }}
                transition={{ duration: 120, ease: "linear" }}
              />
            </motion.div>

            <motion.div
              className="text-sm text-gray-500 mt-2"
              animate={{ opacity: [0.5, 1, 0.5] }}
              transition={{ duration: 2, repeat: Infinity }}
            >
              This may take a couple of minutes...
            </motion.div>
          </motion.div>
        </div>
      ) : (
        <div className="min-h-screen bg-gradient-to-b from-gray-50/50 to-white -m-6">
          {!isTimerActive ? (
            <div className="fixed inset-0 bg-black/50 backdrop-blur-sm z-50 flex items-center justify-center">
              <motion.div
                initial={{ scale: 0.9, opacity: 0 }}
                animate={{ scale: 1, opacity: 1 }}
                className="bg-white p-8 rounded-2xl shadow-xl max-w-md w-full mx-4"
              >
                <h2 className="text-2xl font-bold text-center mb-4">
                  Ready to Begin?
                </h2>
                <p className="text-gray-600 mb-6 text-center">
                  Once you start, you cannot pause or exit the exam. Make sure
                  you're ready!
                </p>
                <button
                  onClick={startExam}
                  className="w-full bg-[var(--primary-color)] text-white py-3 rounded-xl font-medium hover:bg-[var(--primary-color)]/90 transition-colors"
                >
                  Start Exam
                </button>
              </motion.div>
            </div>
          ) : null}
          {hasAttemptedExit && (
            <div className="fixed inset-0 bg-black/50 backdrop-blur-sm z-[100] flex items-center justify-center">
              <div className="bg-white p-8 rounded-2xl shadow-xl max-w-md w-full mx-4">
                <h2 className="text-2xl font-bold text-red-500 text-center mb-4">
                  Exam Submitted
                </h2>
                <p className="text-gray-600 text-center">
                  You attempted to exit the exam. Your responses have been
                  automatically submitted.
                </p>
              </div>
            </div>
          )}
          {/* Modern Header with Glass Effect */}
          <div className="fixed top-0 left-0 right-0 z-50">
            <div className="bg-gradient-to-b from-white via-white/95 to-white/75 backdrop-blur-md shadow-sm border-b border-gray-100">
              <div className="max-w-screen-2xl mx-auto px-8 py-3">
                <div className="flex items-center justify-between">
                  {/* Left Side - Progress */}
                  <div className="flex items-center gap-8">
                    {/* Circular Progress */}
                    <div className="relative w-14 h-14">
                      <svg className="w-full h-full transform -rotate-90">
                        <circle
                          className="text-gray-200"
                          strokeWidth="3"
                          stroke="currentColor"
                          fill="transparent"
                          r="25"
                          cx="28"
                          cy="28"
                        />
                        <circle
                          className="text-[var(--primary-color)]"
                          strokeWidth="3"
                          strokeLinecap="round"
                          stroke="currentColor"
                          fill="transparent"
                          r="25"
                          cx="28"
                          cy="28"
                          strokeDasharray={2 * Math.PI * 25}
                          strokeDashoffset={
                            2 *
                            Math.PI *
                            25 *
                            (1 -
                              (currentQuestionIndex + 1) /
                                questions[examType].length)
                          }
                        />
                      </svg>
                      <div className="absolute inset-0 flex items-center justify-center">
                        <span className="text-sm font-semibold">
                          {currentQuestionIndex + 1}/
                          {questions[examType].length}
                        </span>
                      </div>
                    </div>

                    {/* Linear Progress with Question Type */}
                    <div className="flex flex-col gap-1.5">
                      <div className="flex items-center gap-2">
                        <span className="text-sm font-medium text-gray-600">
                          Progress
                        </span>
                        <span className="text-xs text-[var(--primary-color)] font-semibold">
                          {Math.round(
                            ((currentQuestionIndex + 1) /
                              questions[examType].length) *
                              100
                          )}
                          %
                        </span>
                      </div>
                      <div className="w-[200px] h-1.5 bg-gray-100 rounded-full overflow-hidden">
                        <div
                          className="h-full rounded-full bg-[var(--primary-color)] transition-all duration-300"
                          style={{
                            width: `${
                              ((currentQuestionIndex + 1) /
                                questions[examType].length) *
                              100
                            }%`,
                            backgroundImage:
                              "linear-gradient(90deg, var(--primary-color) 0%, var(--primary-color) 100%)",
                          }}
                        />
                      </div>
                    </div>
                  </div>

                  {/* Center - Navigation Buttons */}
                  <div className="flex items-center gap-3">
                    <button
                      onClick={handlePrevious}
                      disabled={
                        !questions[examType] || currentQuestionIndex === 0
                      }
                      className={`relative group flex items-center gap-2 px-4 py-2 rounded-xl transition-all duration-300
                        ${
                          !questions[examType] || currentQuestionIndex === 0
                            ? "bg-gray-100 text-gray-400"
                            : "bg-[var(--primary-color)]/10 text-[var(--primary-color)] hover:bg-[var(--primary-color)] hover:text-white"
                        }`}
                    >
                      <FaArrowLeft className="text-sm group-hover:-translate-x-1 transition-transform" />
                      <span className="font-medium">Previous</span>
                    </button>

                    <button
                      onClick={handleNext}
                      disabled={
                        !questions[examType] ||
                        currentQuestionIndex === questions[examType]?.length - 1
                      }
                      className={`relative group flex items-center gap-2 px-4 py-2 rounded-xl transition-all duration-300
                        ${
                          !questions[examType] ||
                          currentQuestionIndex ===
                            questions[examType]?.length - 1
                            ? "bg-gray-100 text-gray-400"
                            : "bg-[var(--primary-color)]/10 text-[var(--primary-color)] hover:bg-[var(--primary-color)] hover:text-white"
                        }`}
                    >
                      <span className="font-medium">Next</span>
                      <FaArrowRight className="text-sm group-hover:translate-x-1 transition-transform" />
                    </button>

                    <div className="h-8 w-px bg-gray-200 mx-2"></div>

                    <button
                      onClick={handleExamSubmit}
                      className="bg-[var(--primary-color)] hover:bg-[var(---primary-color)]/90 text-white px-6 py-2 rounded-xl font-medium transition-all duration-300 flex items-center gap-2"
                    >
                      <span>Submit Test</span>
                      <svg
                        className="w-4 h-4"
                        fill="none"
                        stroke="currentColor"
                        viewBox="0 0 24 24"
                      >
                        <path
                          strokeLinecap="round"
                          strokeLinejoin="round"
                          strokeWidth={2}
                          d="M9 5l7 7-7 7"
                        />
                      </svg>
                    </button>
                  </div>

                  {/* Right Side - Timer */}
                  <div className="relative">
                    <div className="bg-white/80 backdrop-blur-sm px-6">
                      <div className="flex items-center gap-3">
                        <div className="relative">
                          <div className="relative bg-[var(--primary-color)]/5 p-2 rounded-full">
                            <FaClock
                              className={`text-lg ${
                                timeLeft < 300
                                  ? "text-red-500"
                                  : "text-[var(--primary-color)]"
                              }`}
                            />
                          </div>
                        </div>
                        <div className="flex items-center gap-1">
                          <div className="bg-gray-50/80 px-2 py-1 rounded-md">
                            <span className="font-mono text-xl font-medium text-gray-700">
                              {String(Math.floor(timeLeft / 60)).padStart(
                                2,
                                "0"
                              )}
                            </span>
                          </div>
                          <span className="text-[var(--primary-color)] font-medium mx-0.5">
                            :
                          </span>
                          <div className="bg-gray-50/80 px-2 py-1 rounded-md">
                            <span className="font-mono text-xl font-medium text-gray-700">
                              {String(timeLeft % 60).padStart(2, "0")}
                            </span>
                          </div>
                        </div>
                      </div>
                    </div>
                    {timeLeft < 300 && (
                      <div className="absolute -bottom-2 left-1/2 transform -translate-x-1/2">
                        <div className="bg-red-50 text-red-500 text-xs px-3 py-0.5 rounded-full border border-red-100">
                          Time running low
                        </div>
                      </div>
                    )}
                  </div>
                </div>
              </div>
            </div>
          </div>

          <div className="pt-24 pb-24">
            <div className="max-w-screen-2xl mx-auto min-h-[calc(100vh-160px)] relative">
              <div className="h-full p-8">
                <QuestionRenderer
                  question={questions[examType][currentQuestionIndex]}
                  answers={answers}
                  handleAnswerSave={handleAnswerSave}
                  paragraphOrder={paragraphOrder}
                  setParagraphOrder={setParagraphOrder}
                  handleDragStart={handleDragStart}
                  handleDragOver={handleDragOver}
                  handleDrop={handleDrop}
                />
              </div>
              {isSubmitting && (
                <div className="fixed inset-0 bg-black/50 backdrop-blur-sm z-[100] flex items-center justify-center">
                  <div className="bg-white p-8 rounded-2xl shadow-xl max-w-md w-full mx-4">
                    <div className="flex flex-col items-center">
                      <div className="animate-spin rounded-full h-16 w-16 border-4 border-[var(--primary-color)] border-t-transparent mb-4"></div>
                      <h2 className="text-xl font-semibold text-gray-700 mb-2">
                        Submitting Your Exam
                      </h2>
                      <p className="text-gray-500 text-center">
                        Please wait while we process your responses...
                      </p>
                    </div>
                  </div>
                </div>
              )}
              {error && (
                <div className="fixed inset-0 bg-black/50 backdrop-blur-sm z-[100] flex items-center justify-center">
                  <div className="bg-white p-8 rounded-2xl shadow-xl max-w-md w-full mx-4">
                    <h2 className="text-2xl font-bold text-red-500 text-center mb-4">
                      Submission Error
                    </h2>
                    <p className="text-gray-600 text-center">{error}</p>
                    <button
                      onClick={() => setError(null)}
                      className="mt-4 bg-[var(--primary-color)] text-white px-6 py-2 rounded-lg"
                    >
                      Close
                    </button>
                  </div>
                </div>
              )}
              {scoreResponse && !isSubmitting && (
                <div className="fixed inset-0 bg-black/50 backdrop-blur-sm z-[100] flex items-center justify-center">
                  <div className="bg-white p-8 rounded-2xl shadow-xl max-w-4xl w-full mx-4 max-h-[80vh] overflow-y-auto">
                    <div className="flex justify-between items-start mb-6">
                      <h2 className="text-2xl font-bold text-[var(--primary-color)]">
                        Exam Results
                      </h2>
                      <button
                        onClick={() => navigate("/dashboard")}
                        className="text-gray-500 hover:text-gray-700"
                      >
                        <svg
                          className="w-6 h-6"
                          fill="none"
                          stroke="currentColor"
                          viewBox="0 0 24 24"
                        >
                          <path
                            strokeLinecap="round"
                            strokeLinejoin="round"
                            strokeWidth={2}
                            d="M6 18L18 6M6 6l12 12"
                          />
                        </svg>
                      </button>
                    </div>
                    <div className="space-y-6">
                      <div className="bg-gray-50 p-4 rounded-lg">
                        <div className="grid grid-cols-2 gap-4">
                          <div>
                            <p className="text-gray-600">Total Score</p>
                            <p className="text-2xl font-bold text-[var(--primary-color)]">
                              {scoreResponse.total_score}/
                              {scoreResponse.max_score}
                            </p>
                          </div>
                          <div>
                            <p className="text-gray-600">CEFR/CLB Level</p>
                            <p className="text-2xl font-bold text-[var(--primary-color)]">
                              {scoreResponse.cefr_clb_level}
                            </p>
                          </div>
                        </div>
                        <div className="mt-4">
                          <p className="text-gray-600">Overall Feedback</p>
                          <p className="text-gray-800">
                            {scoreResponse.overall_feedback}
                          </p>
                        </div>
                      </div>
                      <div>
                        <h3 className="text-lg font-semibold text-gray-700 mb-3">
                          Score Breakdown
                        </h3>
                        <div className="space-y-4">
                          {scoreResponse.score_breakdown.map(
                            (breakdown, index) => (
                              <div
                                key={index}
                                className="border border-gray-100 rounded-lg p-4"
                              >
                                <h4 className="font-medium text-[var(--primary-color)] mb-2">
                                  {breakdown.question_type}
                                </h4>
                                <p className="text-gray-600 mb-2">
                                  Score: {breakdown.score}/{breakdown.max_score}
                                </p>
                                <div className="pl-4 space-y-2">
                                  {breakdown.details.map((detail, i) => (
                                    <div key={i} className="text-sm">
                                      <p className="text-gray-700 font-medium">
                                        Question {detail.question}:{" "}
                                        {detail.score}/{detail.max_score}
                                      </p>
                                      <p className="text-gray-600 mt-1">
                                        {detail.feedback}
                                      </p>
                                      <div className="mt-1 text-gray-500">
                                        <p>
                                          <strong>Enabling Skills:</strong>
                                        </p>
                                        <ul className="list-disc pl-5">
                                          {Object.entries(
                                            detail.enabling_skills
                                          ).map(([skill, feedback]) => (
                                            <li key={skill}>
                                              {skill.charAt(0).toUpperCase() +
                                                skill.slice(1)}
                                              : {feedback}
                                            </li>
                                          ))}
                                        </ul>
                                      </div>
                                    </div>
                                  ))}
                                </div>
                              </div>
                            )
                          )}
                        </div>
                      </div>
                      <div className="flex justify-end pt-4 border-t">
                        <button
                          onClick={() => navigate("/dashboard")}
                          className="bg-[var(--primary-color)] text-white px-6 py-2 rounded-lg hover:bg-[var(--primary-color)]/90 transition-colors"
                        >
                          Back to Dashboard
                        </button>
                      </div>
                    </div>
                  </div>
                </div>
              )}
              <div className="fixed bottom-0 left-0 right-0 backdrop-blur-md">
                <div className="max-w-screen-2xl mx-auto px-8 py-6">
                  <div className="flex justify-end">
                    {isMonitoring && <Monitor />}
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default Reading;
