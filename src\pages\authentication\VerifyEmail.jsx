import React, { useEffect, useState } from "react";
import { useParams } from "react-router-dom";
import Service from "../../services/Service";

const VerifyEmail = () => {
  const [res, setRes] = useState(null);
  const { token } = useParams();
  const [message, setMessage] = useState("");

  useEffect(() => {
    const verifyEmail = async () => {
      try {
        const response = await Service.userVerfyEmailService({ token });
        const data = response.data;
        setRes(data);
      } catch (error) {
        setRes(error);
        setMessage("Error: Unable to verify email.");
      }
    };

    verifyEmail();
  }, [token]);

  return (
    <div>
      <h2>Email Verification</h2>
      {message && <p>{message}</p>}
    </div>
  );
};

export default VerifyEmail;
