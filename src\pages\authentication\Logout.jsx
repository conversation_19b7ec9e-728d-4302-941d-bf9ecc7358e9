import { useState } from "react";
import { useNavigate } from "react-router-dom";
import { motion } from "framer-motion";

import Service from "../../services/Service";
import { RiLogoutCircleLine, RiCloseLine } from "react-icons/ri";

const Logout = () => {
  const navigate = useNavigate();
  const [res, setRes] = useState(null);
  const [showConfirmation, setShowConfirmation] = useState(true);
  const [isLoggingOut, setIsLoggingOut] = useState(false);

  const handleLogoutConfirm = async () => {
    setShowConfirmation(false);
    setIsLoggingOut(true);
    try {
      const userId = sessionStorage.getItem("user_id");
      if (userId) {
        const response = await Service.userLogoutService({ user_id: userId });
        setRes(response);
        sessionStorage.clear();
        setTimeout(() => {
          navigate("/signin");
        }, 1500);
      } else {
        navigate("/signin");
      }
    } catch (error) {
      setRes(error);
      console.error("Logout failed:", error);
      setTimeout(() => {
        navigate("/signin");
      }, 1500);
    }
  };

  const handleCancel = () => {
    navigate(-1);
  };

  return (
    <motion.div
      initial={{ opacity: 0 }}
      animate={{ opacity: 1 }}
      className="min-h-screen flex items-center justify-center bg-gradient-to-br from-[var(--primary-color)] via-[var(--text-color)] to-[var(--background-color)] relative overflow-hidden"
    >
      {/* Animated Background Elements */}
      {[...Array(30)].map((_, i) => (
        <motion.div
          key={i}
          className={`absolute ${
            i % 3 === 0
              ? "bg-[var(--primary-color)]"
              : "bg-[var(--text-color)]"
          } opacity-15`}
          initial={{
            width: Math.random() * 100 + 40,
            height: Math.random() * 100 + 40,
            x: Math.random() * window.innerWidth - window.innerWidth / 2,
            y: Math.random() * window.innerHeight - window.innerHeight / 2,
            rotate: Math.random() * 360,
            borderRadius: i % 5 === 0 
              ? '0% 50% 50% 0% / 50% 50% 50% 50%' // arrow pointing right (logout)
              : i % 5 === 1 
              ? '50%' // speaking bubble circle
              : i % 5 === 2
              ? 'polygon(0% 0%, 100% 0%, 100% 75%, 75% 75%, 75% 100%, 50% 75%, 0% 75%)' // chat bubble
              : i % 5 === 3
              ? 'polygon(40% 0%, 60% 0%, 100% 100%, 0% 100%)' // book/reading shape
              : 'polygon(20% 0%, 80% 0%, 100% 100%, 0% 100%)', // headphone shape
            clipPath: i % 5 === 0 
              ? 'polygon(20% 0%, 100% 0%, 100% 100%, 20% 100%, 0% 50%)' // arrow
              : i % 5 === 1
              ? 'none' // circle for speaking
              : i % 5 === 2
              ? 'polygon(0% 0%, 100% 0%, 100% 75%, 75% 75%, 75% 100%, 50% 75%, 0% 75%)' // chat
              : i % 5 === 3
              ? 'polygon(40% 0%, 60% 0%, 100% 100%, 0% 100%)' // book
              : 'polygon(20% 0%, 80% 0%, 100% 100%, 0% 100%)' // headphone
          }}
          animate={{
            y: [null, Math.random() * -500 + 250],
            x: [null, Math.random() * 500 - 250],
            rotate: [null, Math.random() * 360],
            scale: [1, Math.random() * 0.3 + 0.7],
          }}
          transition={{
            duration: Math.random() * 10 + 8,
            repeat: Infinity,
            repeatType: "reverse",
            ease: "easeInOut",
          }}
        />
      ))}

      {showConfirmation ? (
        <motion.div
          initial={{ scale: 0.9, opacity: 0, y: 50 }}
          animate={{ scale: 1, opacity: 1, y: 0 }}
          className="bg-[var(--background-color)]/95 backdrop-blur-xl p-8 rounded-[2rem] shadow-2xl max-w-md w-full mx-4 relative z-10 border border-[var(--primary-color)]/20"
        >
          <div className="flex items-center justify-center mb-8">
            <motion.div
              animate={{
                rotate: [0, 360],
                scale: [1, 1.1, 1],
              }}
              transition={{
                duration: 3,
                repeat: Infinity,
                ease: "easeInOut",
              }}
              className="w-24 h-24 rounded-[30%] bg-gradient-to-br from-[var(--primary-color)] to-[var(--primary-color)]/70 flex items-center justify-center relative"
            >
              <motion.div
                animate={{ rotate: [0, -360] }}
                transition={{ duration: 3, repeat: Infinity, ease: "linear" }}
              >
                <RiLogoutCircleLine className="text-[var(--text-color)] text-5xl" />
              </motion.div>
            </motion.div>
          </div>

          <motion.h2
            initial={{ y: 20, opacity: 0 }}
            animate={{ y: 0, opacity: 1 }}
            transition={{ delay: 0.2 }}
            className="text-3xl font-bold text-center text-[var(--secondary-color)] mb-3"
          >
            Time to Say Goodbye?
          </motion.h2>
          <motion.p
            initial={{ y: 20, opacity: 0 }}
            animate={{ y: 0, opacity: 1 }}
            transition={{ delay: 0.3 }}
            className="text-center text-[var(--secondary-color)]/70 mb-8"
          >
            We hope to see you back soon!
          </motion.p>

          <div className="flex gap-4">
            <motion.button
              whileHover={{ scale: 1.03, rotate: -2 }}
              whileTap={{ scale: 0.97 }}
              onClick={handleCancel}
              className="flex-1 px-6 py-4 rounded-2xl border-2 cursor-pointer border-[var(--primary-color)] text-[var(--primary-color)] font-medium bg-[var(--background-color)] hover:bg-[var(--primary-color)]/5 transition-colors flex items-center justify-center gap-2 group"
            >
              <RiCloseLine className="text-2xl group-hover:rotate-90 transition-transform" />
              <span>Stay Here</span>
            </motion.button>

            <motion.button
              whileHover={{ scale: 1.03, rotate: 2 }}
              whileTap={{ scale: 0.97 }}
              onClick={handleLogoutConfirm}
              className="flex-1 px-6 py-4 rounded-2xl cursor-pointer bg-[var(--primary-color)] text-[var(--text-color)] font-medium hover:opacity-90 transition-colors flex items-center justify-center gap-2 group shadow-lg"
            >
              <RiLogoutCircleLine className="text-2xl group-hover:rotate-180 transition-transform" />
              <span>Sign Out</span>
            </motion.button>
          </div>
        </motion.div>
      ) : (
        isLoggingOut && (
          <motion.div
            initial={{ scale: 0.9, opacity: 0 }}
            animate={{ scale: 1, opacity: 1 }}
            className="text-center relative z-10"
          >
            <motion.div
              animate={{
                rotate: 360,
                scale: [1, 1.2, 1],
              }}
              transition={{
                duration: 2,
                repeat: Infinity,
                ease: "easeInOut",
              }}
              className="w-24 h-24 mx-auto mb-8 relative"
            >
              <motion.div
                className="absolute inset-0 rounded-full cursor-pointer bg-[var(--primary-color)]"
                animate={{
                  scale: [1, 1.2, 1],
                  opacity: [1, 0.5, 1],
                }}
                transition={{
                  duration: 1.5,
                  repeat: Infinity,
                  ease: "easeInOut",
                }}
              />
              <motion.div
                className="absolute inset-2 rounded-full bg-[var(--text-color)]"
                animate={{
                  rotate: [0, 360],
                  scale: [1, 0.8, 1],
                }}
                transition={{
                  duration: 2,
                  repeat: Infinity,
                  ease: "easeInOut",
                }}
              />
            </motion.div>
            <motion.h2
              animate={{
                y: [0, -10, 0],
              }}
              transition={{
                duration: 2,
                repeat: Infinity,
                ease: "easeInOut",
              }}
              className="text-3xl font-bold mb-3 text-[var(--secondary-color)]"
            >
              See You Soon!
            </motion.h2>
            <p className="text-[var(--secondary-color)]/80">
              Wrapping things up...
            </p>
          </motion.div>
        )
      )}
    </motion.div>
  );
};

export default Logout;
