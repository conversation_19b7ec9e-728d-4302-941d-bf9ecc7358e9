// Add this component inside Reading.jsx or in a separate file
const ReadingQuestionDisplay = ({ question }) => {
  if (!question) return null;

  return (
    <div className="space-y-6">
      {/* Question Header */}
      <div className="bg-white rounded-xl p-6 shadow-sm border border-gray-100">
        <h2 className="text-xl font-bold text-gray-800 mb-2">
          {question.name}
        </h2>
        <p className="text-gray-600">{question.description}</p>
      </div>

      {/* Question Content */}
      <div className="bg-white rounded-xl p-6 shadow-sm border border-gray-100">
        {/* For MCQ Single/Multiple */}
        {question.name.includes("Multiple Choice") && (
          <div className="space-y-6">
            {question.data.passage && (
              <div className="bg-gray-50 p-4 rounded-lg">
                <h3 className="font-medium text-gray-700 mb-3">
                  Read the text:
                </h3>
                <p className="text-gray-600 leading-relaxed">
                  {question.data.passage}
                </p>
              </div>
            )}
            <div>
              <h3 className="font-medium text-gray-700 mb-3">Question:</h3>
              <p className="text-gray-800">{question.data.question}</p>
            </div>
            <div className="space-y-3">
              {question.data.options.map((option, index) => (
                <label
                  key={index}
                  className="flex items-start gap-3 p-3 rounded-lg border border-gray-200 hover:bg-gray-50 cursor-pointer"
                >
                  <input
                    type={
                      question.name.includes("Multiple Answers")
                        ? "checkbox"
                        : "radio"
                    }
                    name={`question-${question.data.id}`}
                    value={option}
                    className="mt-1"
                  />
                  <span className="text-gray-700">{option}</span>
                </label>
              ))}
            </div>
          </div>
        )}

        {/* For Reorder Paragraphs */}
        {question.name === "Re-order Paragraphs" && (
          <div className="space-y-4">
            {question.data.paragraphs.map((paragraph, index) => (
              <div
                key={index}
                draggable
                className="bg-white p-4 rounded-lg border border-gray-200 cursor-move hover:shadow-md transition-shadow"
              >
                <p className="text-gray-700">{paragraph}</p>
              </div>
            ))}
          </div>
        )}
      </div>
    </div>
  );
};

// Then in your main render, replace the QuestionRenderer with:
export default ReadingQuestionDisplay;
