import PhoneInput from "react-phone-number-input";
import "react-phone-number-input/style.css";
import PropTypes from "prop-types";

const PhoneInputComponent = ({ label, value, onChange, required }) => {
  return (
    <div className="w-full">
      {/* Label */}
      {label && (
        <label className="block text font-medium text-black-500 mb-1">
          {label}
        </label>
      )}

      {/* Phone Input */}
      <div className="border border-gray-300 rounded-md px-4 py-2 w-full focus-within:ring focus-within:ring-blue-500">
        <PhoneInput
          placeholder="Enter phone number"
          defaultCountry="IN"
          value={value}
          onChange={onChange}
          className="w-full outline-none border-none bg-transparent"
        />
      </div>
    </div>
  );
};

// ✅ Prop Validation
PhoneInputComponent.propTypes = {
  label: PropTypes.string,
  value: PropTypes.string,
  onChange: PropTypes.func.isRequired,
  required: PropTypes.bool,
};

export default PhoneInputComponent;
