{"name": "pte-frontend", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite --mode production", "build": "vite build", "lint": "eslint .", "preview": "vite preview"}, "dependencies": {"@tailwindcss/vite": "^4.1.5", "axios": "^1.9.0", "framer-motion": "^12.10.4", "react": "^19.0.0", "react-dom": "^19.0.0", "react-icons": "^5.5.0", "react-phone-number-input": "^3.4.12", "react-router-dom": "^7.5.3", "react-toastify": "^11.0.5", "tailwindcss": "^4.1.5"}, "devDependencies": {"@eslint/js": "^9.22.0", "@types/react": "^19.0.10", "@types/react-dom": "^19.0.4", "@vitejs/plugin-react-swc": "^3.8.0", "eslint": "^9.22.0", "eslint-plugin-react-hooks": "^5.2.0", "eslint-plugin-react-refresh": "^0.4.19", "globals": "^16.0.0", "vite": "^6.3.1"}}