import React, { useEffect } from "react";
import { useNavigate } from "react-router-dom";
import { toast } from "react-toastify";
import service from "../../services/Service";

const SessionValidation = ({ children }) => {
  const navigate = useNavigate();

  useEffect(() => {
    const validateSession = async () => {
      const userId = localStorage.getItem("user_id");
      const sessionId = localStorage.getItem("session_id");

      // If user is not logged in, stop further execution
      if (!userId || !sessionId) {
        console.log("No active session found. Skipping validation.");
        return;
      }

      try {
        const response = await service.sessionValidationService({
          user_id: userId,
          session_id: sessionId,
        });
        if (response.ok && response.data.message === "Session is valid.") {
          console.log("Session validated successfully.");
        } else {
          toast.error("Session has expired. Please log in again.", {
            position: "top-center",
          });
          localStorage.clear();
          navigate("/");
        }
      } catch (error) {
        console.error("Session validation error:", error);
        toast.error("Error validating session. Please log in again.", {
          position: "top-center",
        });
        localStorage.clear();
        navigate("/");
      }
    };

    const userId = localStorage.getItem("user_id");
    const sessionId = localStorage.getItem("session_id");

    // Only set up interval if user is logged in
    if (userId && sessionId) {
      // Call validateSession on component mount
      validateSession();

      // Call validateSession every 5 seconds
      const interval = setInterval(() => {
        console.log("Triggering session validation.");
        validateSession();
      }, 5000);

      return () => clearInterval(interval); // Cleanup interval on unmount
    }
  }, [navigate]);

  return <>{children}</>;
};

export default SessionValidation;
