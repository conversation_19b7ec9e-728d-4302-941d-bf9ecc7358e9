import { useState } from "react";
import Button from "../../components/field/Button";
import Input from "../../components/field/Input";
import { IoArrowBack } from "react-icons/io5";
import { useNavigate } from "react-router-dom";
import loginImage from "../../assets/girlthinking.svg";

import Toastify from "../../components/popup/Toastify";
import Service from "../../services/Service";

const ForgetPassword = () => {
  const navigate = useNavigate();
  const [email, setEmail] = useState("");
  const [res, setRes] = useState(null);

  const handleSubmit = async (e) => {
    e.preventDefault();
    try {
      const response = await Service.userForgetService({ email });
      setRes(response);
      sessionStorage.setItem("reset_email", email);
      navigate("/reset-password");
    } catch (error) {
      setRes(error);
    }
  };

  return (
    <div className="min-h-screen flex items-center justify-center bg-[var(--text-color)] relative">
      <Toastify res={res} resClear={() => setRes(null)} />
      <button
        onClick={() => navigate("/signin")}
        className="absolute cursor-pointer top-4 left-4 flex items-center gap-2 text-[var(--primary-color)] hover:opacity-80 transition-all"
      >
        <IoArrowBack className="w-6 h-6" />
        <span className="font-medium">Back to Sign In</span>
      </button>

      <div className="container mx-auto">
        <div className="flex flex-col md:flex-row items-center justify-between bg-[var(--text-color)] rounded-xl shadow-lg overflow-hidden">
          {/* Left Side - Image */}
          <div className="hidden md:block w-1/2 p-12 bg-[var(--text-color)]">
            <div className="relative h-full">
              <div className="absolute w-[400px] h-[400px] bg-[var(--primary-color)] rounded-full -z-10 opacity-10"></div>
              <img
                src={loginImage}
                alt="Forgot Password"
                className="relative z-10 w-full h-full object-contain"
              />
            </div>
          </div>

          {/* Right Side - Form */}
          <div className="w-full md:w-1/2 p-8 lg:p-12">
            <div>
              <h2 className="text-3xl font-extrabold text-[var(--secondary-color)]">
                Forgot Password?
              </h2>
              <p className="mt-2 text-sm text-gray-600">
                Enter your email address to receive a verification code
              </p>
            </div>

            <form className="mt-8 space-y-6" onSubmit={handleSubmit}>
              <Input
                type="email"
                name="email"
                label="Email address"
                value={email}
                onChange={(e) => setEmail(e.target.value)}
                required
                placeholder="Enter your email"
              />

              <Button
                type="submit"
                name="Send OTP"
                className="w-full cursor-pointer bg-[var(--primary-color)] text-[var(--text-color)] hover:opacity-90 py-3 rounded-lg font-medium transition-all"
              />
            </form>
          </div>
        </div>
      </div>
    </div>
  );
};

export default ForgetPassword;
