import { Routes, Route, useLocation } from "react-router-dom";
import Navbar from "./components/layout/Navbar";
import ProductedRoute from "./pages/productedroute/ProductedRoute";
import Hero from "./pages/home/<USER>";
import About from "./pages/home/<USER>";
import Testimonial from "./pages/home/<USER>";
import Contact from "./pages/home/<USER>";
import Footer from "./pages/home/<USER>";
import SignUp from "./pages/authentication/SignUp";
import SignIn from "./pages/authentication/SignIn";
import ForgetPassword from "./pages/authentication/ForgetPassword";
import ResetPassword from "./pages/authentication/ResetPassword";
import DashboardLayout from "./pages/dashboard/DashboardLayout";
import Overview from "./pages/dashboard/Overview";
import Speaking from "./pages/dashboard/Speaking";
import Writing from "./pages/dashboard/Writing";

import Listening from "./pages/dashboard/Listening";
import ReadingPractice from "./pages/dashboard/reading/ReadingPractice";
import ReadingInstructions from "./pages/dashboard/reading/ReadingInstructions";
import Logout from "./pages/authentication/Logout";
import SessionValidation from "./pages/authentication/SessionValidation";
import Reading from "./pages/dashboard/reading/Reading";

function App() {
  const location = useLocation();
  const isAuthPage =
    location.pathname === "/signup" ||
    location.pathname === "/signin" ||
    location.pathname === "/forgot-password" ||
    location.pathname === "/reset-password" ||
    location.pathname === "/logout";

  const isDashboardPage = location.pathname.startsWith("/dashboard");
  const reading = location.pathname.startsWith("/reading-instructions");
  const readingExam = location.pathname.startsWith("/reading-module");

  return (
    <div className="min-h-screen">
      {/* Only show Navbar if not on auth pages AND not on dashboard pages */}
      {!isAuthPage && !isDashboardPage && !reading && !readingExam && (
        <Navbar />
      )}
      <SessionValidation>
        <Routes>
          <Route
            path="/"
            element={
              <>
                <section id="home">
                  <Hero />
                </section>
                <section id="about">
                  <About />
                </section>
                <section id="testimonials">
                  <Testimonial />
                </section>
                <section id="contact">
                  <Contact />
                </section>
              </>
            }
          />
          <Route path="/signup" element={<SignUp />} />
          <Route path="/signin" element={<SignIn />} />
          <Route path="/forgot-password" element={<ForgetPassword />} />
          <Route path="/reset-password" element={<ResetPassword />} />
          <Route path="/logout" element={<Logout />} />

          {/* Dashboard Routes */}
          <Route
            path="/dashboard"
            element={
           
                <DashboardLayout />
            
            }
          >
            <Route index element={<Overview />} />
            <Route path="speaking" element={<Speaking />} />
            <Route path="writing" element={<Writing />} />
            <Route path="reading" element={<Reading />} />
            <Route path="listening" element={<Listening />} />
          </Route>
          <Route path="/reading" element={<ReadingPractice />} />
          <Route
            path="/reading-instructions"
            element={
           
                <ReadingInstructions />
             
            }
          />
          <Route
            path="/reading-module"
            element={
             
                <Reading />
          
            }
          />
        </Routes>
      </SessionValidation>
      {/* Only show Footer if not on auth pages AND not on dashboard pages */}
      {!isAuthPage && !isDashboardPage && !reading && !readingExam && (
        <Footer />
      )}
    </div>
  );
}

export default App;
