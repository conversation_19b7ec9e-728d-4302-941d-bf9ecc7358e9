import { useState, useEffect } from "react";
import { Outlet } from "react-router-dom";
import { motion, AnimatePresence } from "framer-motion";
import Sidebar from "../../components/layout/Sidebar";
import Topbar from "../../components/layout/Topbar";
// Change this line

// To this
import { PROJECT_NAME } from "../../components/common/Name";

const DashboardLayout = () => {
  const [showWelcome, setShowWelcome] = useState(true);
  const userEmail = sessionStorage.getItem("user_email");

  useEffect(() => {
    const timer = setTimeout(() => {
      setShowWelcome(false);
    }, 3000);
    return () => clearTimeout(timer);
  }, []);

  return (
    <div className="flex">
      <AnimatePresence>
        {showWelcome ? (
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            className="fixed inset-0 bg-[var(--background-color)] z-50 flex items-center justify-center overflow-hidden"
          >
            {/* Modern geometric shapes */}
            {[...Array(20)].map((_, i) => (
              <motion.div
                key={i}
                className="absolute"
                initial={{
                  opacity: 0,
                  scale: 0,
                  rotate: 0,
                  x: 0,
                  y: 0,
                }}
                animate={{
                  opacity: [0, 0.3, 0],
                  scale: [1, 2],
                  rotate: [0, 90],
                  x: [0, Math.sin(i) * 200],
                  y: [0, Math.cos(i) * 200],
                }}
                transition={{
                  duration: 2,
                  repeat: Infinity,
                  repeatType: "reverse",
                  ease: "easeInOut",
                  delay: i * 0.1,
                }}
              >
                <div
                  className={`
                  w-20 h-20 relative
                  ${
                    i % 3 === 0
                      ? "bg-[var(--primary-color)]"
                      : "border-2 border-[var(--primary-color)]"
                  }
                  ${i % 2 === 0 ? "rounded-full" : "rounded-lg"}
                  blur-sm flex items-center justify-center
                `}
                >
                  <motion.span
                    className="text-[var(--text-color)] text-2xl font-bold absolute"
                    animate={{
                      rotate: [0, 360],
                      scale: [1, 1.2, 1],
                    }}
                    transition={{
                      duration: 3,
                      repeat: Infinity,
                      ease: "linear",
                    }}
                  >
                    {i % 4 === 0
                      ? "P"
                      : i % 4 === 1
                      ? "T"
                      : i % 4 === 2
                      ? "E"
                      : "★"}
                  </motion.span>
                </div>
              </motion.div>
            ))}

            {/* Central welcome content */}
            <motion.div
              className="relative z-10 flex flex-col items-center"
              initial={{ scale: 0.8, opacity: 0 }}
              animate={{ scale: 1, opacity: 1 }}
              transition={{ duration: 0.5, delay: 0.2 }}
            >
              <motion.div
                className="w-32 h-32 rounded-xl bg-[var(--primary-color)] relative mb-8 overflow-hidden"
                animate={{
                  rotate: [0, 90, 180, 270, 360],
                  borderRadius: ["0%", "25%", "50%", "25%", "0%"],
                }}
                transition={{
                  duration: 3,
                  ease: "easeInOut",
                  repeat: Infinity,
                }}
              >
                <motion.div
                  className="absolute inset-0 bg-[var(--primary-color)]/80"
                  animate={{
                    scale: [1, 1.2, 1],
                    opacity: [0.5, 1, 0.5],
                  }}
                  transition={{
                    duration: 2,
                    repeat: Infinity,
                  }}
                />
              </motion.div>

              <motion.div
                className="text-center relative"
                initial={{ y: 20 }}
                animate={{ y: 0 }}
              >
                <motion.h1
                  className="text-4xl font-bold text-[var(--primary-color)] mb-4"
                  animate={{
                    scale: [1, 1.1, 1],
                  }}
                  transition={{
                    duration: 2,
                    repeat: Infinity,
                  }}
                >
                  Welcome to {PROJECT_NAME}
                </motion.h1>
                <motion.div
                  className="text-lg text-[var(--primary-color)]/80"
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ delay: 0.5 }}
                >
                  <span className="font-medium">{userEmail}</span>
                  <motion.div
                    className="h-0.5 w-0 bg-[var(--primary-color)]"
                    animate={{ width: "100%" }}
                    transition={{ duration: 1, delay: 0.8 }}
                  />
                </motion.div>
              </motion.div>
            </motion.div>
          </motion.div>
        ) : (
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            className="flex w-full"
          >
            <Sidebar />
            <div className="flex-1 ml-64"> {/* Add margin-left to account for sidebar width */}
              <Topbar />
              <motion.div
                initial={{ y: 20, opacity: 0 }}
                animate={{ y: 0, opacity: 1 }}
                transition={{ delay: 0.2 }}
                className="p-6 bg-gray-50 min-h-[calc(100vh-64px)] overflow-y-auto"
              >
                <Outlet />
              </motion.div>
            </div>
          </motion.div>
        )}
      </AnimatePresence>
    </div>
  );
};

export default DashboardLayout;
