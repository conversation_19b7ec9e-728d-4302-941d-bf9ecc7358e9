import { defineConfig, loadEnv } from "vite";
import react from "@vitejs/plugin-react-swc";
import tailwindcss from "@tailwindcss/vite";

export default defineConfig(({ mode }) => {
  /* eslint-disable no-undef */
  const env = loadEnv(mode, process.cwd());
  return {
    plugins: [react(), tailwindcss()],
    define: {
      "import.meta.env.VITE_ENV": JSON.stringify(env.VITE_ENV),
      "import.meta.env.VITE_API_URL": JSON.stringify(env.VITE_API_URL),
      "import.meta.env.VITE_LOGIN_URL": JSON.stringify(env.VITE_LOGIN_URL),
      "import.meta.env.VITE_FORGET_URL": JSON.stringify(env.VITE_FORGET_URL),
      "import.meta.env.VITE_READINGEXAM_URL": JSON.stringify(
        env.VITE_READINGEXAM_URL
      ),
      "import.meta.env.VITE_PAYEMENT_URL": JSON.stringify(
        env.VITE_PAYEMENT_URL
      ),
      "import.meta.env.VITE_MONITOR_URL": JSON.stringify(env.VITE_MONITOR_URL),
      "import.meta.env.VITE_READING_EVEALUTION_URL": JSON.stringify(
        env.VITE_READING_EVEALUTION_URL
      ),
    },
    server: {
      host: "0.0.0.0",
      port: 5173,
      strictPort: true,
      allowedHosts: ["ptegenai.com"], // Add this line
    },
  };
});
