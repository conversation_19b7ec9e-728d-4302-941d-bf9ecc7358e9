import React from "react";
import { FaFacebook, <PERSON>a<PERSON><PERSON><PERSON>, FaInstagram, FaLinkedin } from "react-icons/fa";
import { PROJECT_NAME } from "../../components/common/Name";

const Footer = () => {
  return (
    <footer className="bg-[var(--secondary-color)] text-white py-12">
      <div className="container mx-auto px-4">
        <div className="grid grid-cols-1 md:grid-cols-4 gap-8">
          {/* Company Info */}
          <div>
            <h3 className="text-2xl font-bold mb-4">
              {PROJECT_NAME}
              <span className="text-[var(--primary-color)]">.</span>
            </h3>
            <p className="text-gray-300 mb-4">
              Helping students achieve their desired PTE Academic scores through
              comprehensive training and practice.
            </p>
            <div className="flex space-x-4">
              <a href="#" className="hover:text-[var(--primary-color)]">
                <FaFacebook size={24} />
              </a>
              <a href="#" className="hover:text-[var(--primary-color)]">
                <FaTwitter size={24} />
              </a>
              <a href="#" className="hover:text-[var(--primary-color)]">
                <FaInstagram size={24} />
              </a>
              <a href="#" className="hover:text-[var(--primary-color)]">
                <FaLinkedin size={24} />
              </a>
            </div>
          </div>

          {/* Quick Links */}
          <div>
            <h4 className="text-xl font-semibold mb-4">Quick Links</h4>
            <ul className="space-y-2">
              <li>
                <a href="#home" className="hover:text-[var(--primary-color)]">
                  Home
                </a>
              </li>
              <li>
                <a href="#about" className="hover:text-[var(--primary-color)]">
                  About Us
                </a>
              </li>
              <li>
                <a
                  href="#testimonials"
                  className="hover:text-[var(--primary-color)]"
                >
                  Testimonials
                </a>
              </li>
              <li>
                <a
                  href="#contact"
                  className="hover:text-[var(--primary-color)]"
                >
                  Contact
                </a>
              </li>
            </ul>
          </div>

          {/* PTE Modules */}
          <div>
            <h4 className="text-xl font-semibold mb-4">PTE Modules</h4>
            <ul className="space-y-2">
              <li>
                <a href="#" className="hover:text-[var(--primary-color)]">
                  Speaking
                </a>
              </li>
              <li>
                <a href="#" className="hover:text-[var(--primary-color)]">
                  Writing
                </a>
              </li>
              <li>
                <a href="#" className="hover:text-[var(--primary-color)]">
                  Reading
                </a>
              </li>
              <li>
                <a href="#" className="hover:text-[var(--primary-color)]">
                  Listening
                </a>
              </li>
            </ul>
          </div>

          {/* Contact Info */}
          <div>
            <h4 className="text-xl font-semibold mb-4">Contact Info</h4>
            <ul className="space-y-2">
              <li>Email: <EMAIL></li>
              <li>Phone: ****** 567 8900</li>
             
            </ul>
          </div>
        </div>

        <div className="border-t border-gray-700 mt-8 pt-8 text-center">
          <p className="text-gray-400">
            {new Date().getFullYear()} {PROJECT_NAME}. All rights reserved.
          </p>
        </div>
      </div>
    </footer>
  );
};

export default Footer;
