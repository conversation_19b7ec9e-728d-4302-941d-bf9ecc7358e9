import TestLayout from "./components/TestLayout";

const Listening = () => {
  const academicTests = [
    {
      name: "Summarize Spoken Text",
      description: "Write a 50-70 word summary of the recording",
      duration: "8 minutes",
    },
    {
      name: "Multiple Choice, Multiple Answers",
      description: "Select multiple correct answers based on the recording",
      duration: "1-2 minutes",
    },
    {
      name: "Fill in the Blanks",
      description: "Type the missing words while listening",
      duration: "2-3 minutes",
    },
    {
      name: "Highlight Correct Summary",
      description: "Choose the best summary of the recording",
      duration: "1-2 minutes",
    },
    {
      name: "Select Missing Word",
      description: "Choose the missing word or group of words",
      duration: "1 minute",
    },
    {
      name: "Highlight Incorrect Words",
      description: "Identify words that differ from the recording",
      duration: "2-3 minutes",
    },
    {
      name: "Write from Dictation",
      description: "Type the sentence you hear",
      duration: "3-4 minutes",
    },
  ];

  const coreTests = [
    {
      name: "Summarize Spoken Text",
      description: "Write a 50-70 word summary of the recording",
      duration: "8 minutes",
    },
    {
      name: "Fill in the Blanks",
      description: "Type the missing words while listening",
      duration: "2-3 minutes",
    },
    {
      name: "Write from Dictation",
      description: "Type the sentence you hear",
      duration: "3-4 minutes",
    },
  ];

  return (
    <TestLayout section="Listening Practice">
      {({ examType }) => (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {(examType === "academic" ? academicTests : coreTests).map(
            (test, index) => (
              <div
                key={index}
                className="bg-white p-6 rounded-xl shadow-sm hover:shadow-md transition-all"
              >
                <h3 className="font-semibold text-lg mb-2">{test.name}</h3>
                <p className="text-gray-500 text-sm mb-2">{test.description}</p>
                <div className="flex items-center gap-2 text-sm text-gray-400 mb-4">
                  <span>⏱️ {test.duration}</span>
                </div>
                <div className="flex justify-between items-center">
                  <span className="text-sm text-gray-400">0 Attempts</span>
                  <button className="bg-[var(--primary-color)] text-white px-4 py-2 rounded-lg text-sm">
                    Start Practice
                  </button>
                </div>
              </div>
            )
          )}
        </div>
      )}
    </TestLayout>
  );
};

export default Listening;
