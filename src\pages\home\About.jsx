import React, { useState } from "react";

const About = () => {
  const [activeSection, setActiveSection] = useState(null);

  const prepData = [
    {
      title: "Mock Tests",
      description: "AI-powered PTE mock tests that simulate real exam conditions with instant scoring and feedback",
    },
    {
      title: "Speaking Practice",
      description: "Advanced speech recognition technology to evaluate pronunciation, fluency, and oral proficiency",
    },
    {
      title: "Writing Analysis",
      description: "Detailed writing assessments with AI-driven feedback on structure, grammar, and vocabulary",
    },
  ];

  const featuresData = [
    {
      title: "Real-Time Scoring",
      feature: "Get instant results and detailed performance analysis after each practice session",
    },
    {
      title: "Performance Tracking",
      feature: "Track your progress across all PTE sections with detailed analytics and improvement suggestions",
    },
    {
      title: "Expert Support",
      feature: "24/7 access to practice materials and comprehensive study resources",
    },
  ];

  return (
    <div className="container mx-auto px-4 py-16 bg-[#f5f5f5]" id="about">
      <div className="text-center mb-12">
        <span className="text-[var(--primary-color)]">— Practice & Prepare</span>
        <h2 className="text-3xl font-bold mt-2">
          Complete PTE Preparation Platform
          <span className="text-[var(--primary-color)]">*</span>
        </h2>
      </div>

      <div className="flex justify-between gap-8 max-w-5xl mx-auto">
        <div
          className="w-1/2 cursor-pointer"
          onMouseEnter={() => setActiveSection("prep")}
          onMouseLeave={() => setActiveSection(null)}
        >
          <div className="flex items-center gap-2 mb-6">
            <div className="w-8 h-8 bg-[var(--primary-color)] rounded-full flex items-center justify-center">
              <span className="text-white">P</span>
            </div>
            <h3 className="text-2xl font-bold">Practice Tests</h3>
          </div>

          <div className="relative">
            <div className="absolute left-4 top-0 bottom-0 w-0.5 bg-[var(--primary-color)]"></div>

            {prepData.map((item, index) => (
              <div
                key={index}
                className={`ml-8 mb-8 transition-all duration-300 ${
                  activeSection === "prep"
                    ? "opacity-100 translate-y-0"
                    : "opacity-70 translate-y-2"
                }`}
              >
                <div className="flex items-center justify-between mb-2">
                  <h4 className="font-bold">{item.title}</h4>
                </div>
                <p className="text-gray-600">{item.description}</p>
              </div>
            ))}
          </div>
        </div>

        <div
          className="w-1/2 cursor-pointer"
          onMouseEnter={() => setActiveSection("features")}
          onMouseLeave={() => setActiveSection(null)}
        >
          <div className="flex items-center gap-2 mb-6">
            <div className="w-8 h-8 bg-[var(--primary-color)] rounded-full flex items-center justify-center">
              <span className="text-white">F</span>
            </div>
            <h3 className="text-2xl font-bold">Key Features</h3>
          </div>

          <div className="relative">
            <div className="absolute left-4 top-0 bottom-0 w-0.5 bg-[var(--primary-color)]"></div>

            {featuresData.map((item, index) => (
              <div
                key={index}
                className={`ml-8 mb-8 transition-all duration-300 ${
                  activeSection === "features"
                    ? "opacity-100 translate-y-0"
                    : "opacity-70 translate-y-2"
                }`}
              >
                <div className="flex items-center justify-between mb-2">
                  <h4 className="font-bold">{item.title}</h4>
                </div>
                <p className="text-gray-600">{item.feature}</p>
              </div>
            ))}
          </div>
        </div>
      </div>
    </div>
  );
};

export default About;
