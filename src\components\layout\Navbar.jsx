import { PROJECT_NAME } from "../common/Name";
import { useNavigate } from "react-router-dom";
import { useState } from "react";

const Navbar = () => {
  const navigate = useNavigate();
  const [activeSection, setActiveSection] = useState("home");

  const scrollToSection = (sectionId) => {
    const element = document.getElementById(sectionId);
    element?.scrollIntoView({ behavior: "smooth" });
    setActiveSection(sectionId);
  };

  const getNavButtonClass = (section) => {
    return `relative cursor-pointer transition-all ${
      activeSection === section
        ? "text-[var(--primary-color)] font-medium after:content-[''] after:absolute after:-bottom-2 after:left-0 after:w-full after:h-0.5 after:bg-[var(--primary-color)]"
        : "text-gray-600 hover:text-[var(--primary-color)]"
    }`;
  };

  return (
    <nav className="py-4 px-8 bg-white fixed w-full z-50">
      <div className="container mx-auto">
        <div className="flex justify-between items-center">
          <div className="flex items-center gap-2">
            <div className="w-8 h-8 bg-[var(--primary-color)] rounded-full flex items-center justify-center">
              <span className="text-white font-bold">P</span>
            </div>
            <span className="text-xl font-bold">{PROJECT_NAME}.</span>
          </div>

          <div className="hidden md:flex items-center space-x-8">
            <button
              onClick={() => scrollToSection("home")}
              className={getNavButtonClass("home")}
            >
              Home
            </button>
            <button
              onClick={() => scrollToSection("about")}
              className={getNavButtonClass("about")}
            >
              About
            </button>
            <button
              onClick={() => scrollToSection("testimonials")}
              className={getNavButtonClass("testimonials")}
            >
              Testimonials
            </button>
            <button
              onClick={() => navigate("/signin")}
              className={getNavButtonClass("signin")}
            >
              SignUp / SignIn
            </button>
          </div>

          <button
            onClick={() => scrollToSection("contact")}
            className="bg-black cursor-pointer text-white px-6 py-2 rounded-full hover:bg-gray-800"
          >
            Contact
          </button>
        </div>
      </div>
    </nav>
  );
};

export default Navbar;
