import Service from "../../../services/Service";

export const handleSubmit = async (examType, questions, answers) => {
  try {
    const scoreRequest = {
      exam_type: examType,
      fill_rw_responses: [],
      fill_reading_responses: [],
      mcq_single_responses: [],
      mcq_multiple_responses: [],
      reorder_paragraph_responses: [],
    };

    questions.forEach((question, index) => {
      const questionId = question.data.id;
      const userAnswers = answers[questionId] || {};
      const correctAnswers =
        question.data.correct_answers ||
        question.data.answers ||
        question.data.correct_order ||
        [];

      let normalizedUserAnswers;
      let questionType;

      switch (question.name) {
        case "Reading & Writing: Fill in the Blanks":
          questionType = "FillRw";
          normalizedUserAnswers = Object.values(userAnswers).filter(
            (ans) => ans !== undefined && ans !== null && ans !== ""
          );
          break;
        case "Reading: Fill in the Blanks":
          questionType = "FillReading";
          normalizedUserAnswers = Object.values(userAnswers).filter(
            (ans) => ans !== undefined && ans !== null && ans !== ""
          );
          break;
        case "Multiple Choice, Single Answer":
          questionType = "McqSingle";
          normalizedUserAnswers = [userAnswers].filter(
            (ans) => ans !== undefined && ans !== null && ans !== ""
          );
          break;
        case "Multiple Choice, Multiple Answers":
          questionType = "McqMultiple";
          normalizedUserAnswers = Array.isArray(userAnswers)
            ? userAnswers.filter(
                (ans) => ans !== undefined && ans !== null && ans !== ""
              )
            : [];
          break;
        case "Re-order Paragraphs":
          questionType = "ReorderParagraph";
          normalizedUserAnswers = Array.isArray(userAnswers)
            ? userAnswers
            : question.data.paragraphs?.map((_, index) => index) || [];
          break;
        default:
          console.warn(
            `Unknown question type: ${question.name} at index ${index}`
          );
          return;
      }

      // Log answers for debugging Case 2
      console.log(`Question ${questionId} (${questionType}):`, {
        userAnswers: normalizedUserAnswers,
        correctAnswers,
        rawUserAnswers: userAnswers,
      });

      // Validate answer lengths
      if (
        questionType !== "McqMultiple" &&
        normalizedUserAnswers.length !== correctAnswers.length
      ) {
        console.warn(
          `Answer length mismatch for question ${questionId} (${questionType}): ` +
            `user_answers (${normalizedUserAnswers.length}) vs correct_answers (${correctAnswers.length})`
        );
        normalizedUserAnswers = normalizedUserAnswers.slice(
          0,
          correctAnswers.length
        );
        while (normalizedUserAnswers.length < correctAnswers.length) {
          normalizedUserAnswers.push("");
        }
      }

      const response = {
        question_type: questionType,
        user_answers: normalizedUserAnswers,
        correct_answers: Array.isArray(correctAnswers)
          ? correctAnswers
          : [correctAnswers].filter(
              (ans) => ans !== undefined && ans !== null && ans !== ""
            ),
      };

      switch (questionType) {
        case "FillRw":
          scoreRequest.fill_rw_responses.push(response);
          break;
        case "FillReading":
          scoreRequest.fill_reading_responses.push(response);
          break;
        case "McqSingle":
          scoreRequest.mcq_single_responses.push(response);
          break;
        case "McqMultiple":
          scoreRequest.mcq_multiple_responses.push(response);
          break;
        case "ReorderParagraph":
          scoreRequest.reorder_paragraph_responses.push(response);
          break;
      }
    });

    console.log(
      "Submitting scoreRequest:",
      JSON.stringify(scoreRequest, null, 2)
    );

    const response = await Service.userReadingEvalutionService(scoreRequest);

    return { data: response.data, error: null };
  } catch (err) {
    console.error("Error submitting answers:", err);
    let errorMessage = "Failed to submit answers. Please try again.";
    if (err.response?.status === 422) {
      errorMessage = `Submission failed: ${
        err.response.data.message || "Invalid data provided."
      }`;
      console.error(
        "Backend error details:",
        JSON.stringify(err.response.data, null, 2)
      );
    }
    return { data: null, error: errorMessage };
  }
};
