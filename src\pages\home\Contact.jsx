import React from "react";
import contactImage from "../../assets/boypose.svg";
import Input from "../../components/field/Input";
import Button from "../../components/field/Button";
import PhoneInputComponent from "../../components/field/PhoneInputComponent";

const Contact = () => {
  const [formData, setFormData] = React.useState({
    fullName: "",
    email: "",

    phone: "",
    message: "",
  });

  const handleChange = (e) => {
    const { name, value } = e.target;
    setFormData((prev) => ({
      ...prev,
      [name]: value,
    }));
  };

  const handlePhoneChange = (value) => {
    setFormData((prev) => ({
      ...prev,
      phone: value,
    }));
  };

  const handleSubmit = (e) => {
    e.preventDefault();
    console.log(formData);
  };

  return (
    <div className="bg-[#f5f5f5] py-16" id="contact">
      <div className="container mx-auto px-4">
        <div className="text-center mb-12">
          <span className="text-[var(--primary-color)]">— Get in Touch</span>
          <h2 className="text-3xl font-bold mt-2">
            Contact <span className="text-[var(--primary-color)]">Us</span>
          </h2>
        </div>

        <div className="flex flex-col md:flex-row gap-8 max-w-6xl mx-auto">
          {/* Contact Form */}
          <div className="w-full md:w-1/2">
            <form onSubmit={handleSubmit} className="space-y-6">
              <Input
                type="text"
                name="fullName"
                label="Full Name"
                value={formData.fullName}
                onChange={handleChange}
                placeholder="Enter your name"
                required
              />

              <Input
                type="email"
                name="email"
                label="Email Address"
                value={formData.email}
                onChange={handleChange}
                placeholder="Enter your email"
                required
              />

              <PhoneInputComponent
                label="Phone Number"
                value={formData.phone}
                onChange={handlePhoneChange}
                required
              />

              <Input
                type="textarea"
                name="message"
                label="Message"
                value={formData.message}
                onChange={handleChange}
                placeholder="Write your message here..."
                required
                maxLength={500}
                className="h-22"
              />
              <div className="text-sm text-gray-500  flex justify-end">
                {formData.message.length}/500 characters
                {formData.message.length === 500 && (
                  <span className="text-red-500">Maximum length reached!</span>
                )}
              </div>

              <Button
                type="submit"
                name="Send Message"
                className="w-full bg-[var(--primary-color)] text-white py-3 rounded-lg cursor-pointer hover:bg-[var(--secondary-color)] transition duration-300"
              />
            </form>
          </div>

          {/* Image Section */}
          <div className="w-full md:w-1/2 flex items-center justify-center  ">
            <div className="relative">
              <div className="absolute w-[400px] h-[400px] bg-[var(--primary-color)] rounded-full -z-10 opacity-10"></div>
              <img
                src={contactImage}
                alt="Contact"
                className="relative  z-10 max-w-full h-auto"
              />
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default Contact;
