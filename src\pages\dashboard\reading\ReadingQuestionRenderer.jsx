import React, { useEffect } from "react";
import ReadingPassageDisplay from "./ReadingPassageDisplay";

const ReadingQuestionRenderer = ({
  question,
  answers,
  handleAnswerSave,
  paragraphOrder,
  setParagraphOrder,
  handleDragStart,
  handleDragOver,
  handleDrop,
}) => {
  // Reset paragraphOrder and log question state
  useEffect(() => {
    if (question?.name === "Re-order Paragraphs") {
      setParagraphOrder(
        answers[question.data.id] ||
          question.data.paragraphs?.map((_, index) => index) ||
          []
      );
    } else {
      setParagraphOrder([]);
    }
    console.log(`Rendering question ${question.data.id} (${question.name}):`, {
      savedAnswers: answers[question.data.id],
    });
  }, [question, answers, setParagraphOrder]);

  if (!question || !question.data) {
    return <div>Loading question...</div>;
  }

  switch (question.name) {
    case "Reading & Writing: Fill in the Blanks":
      return (
        <div className="space-y-4">
          <h3 className="font-semibold text-lg text-[var(--primary-color)]">
            {question.name}
          </h3>
          <p className="text-gray-600 mb-4">{question.description}</p>
          <div className="bg-white p-6 rounded-lg shadow-sm">
            <div className="text-gray-800 leading-relaxed">
              {question.data.paragraph &&
                question.data.options &&
                question.data.paragraph
                  .split(/____\d+____/)
                  .map((part, index, array) => (
                    <React.Fragment key={`fragment-${question.data.id}-${index}`}>
                      <span>{part}</span>
                      {index < array.length - 1 &&
                        question.data.options[index] && (
                          <select
                            className="mx-2 my-3 px-3 py-1 border rounded-md border-[var(--primary-color)] focus:outline-none focus:ring-2"
                            value={answers[question.data.id]?.[index] || ""}
                            onChange={(e) => {
                              const newAnswers = {
                                ...(answers[question.data.id] || {}),
                                [index]: e.target.value,
                              };
                              handleAnswerSave(question.data.id, newAnswers);
                              console.log(`Saved dropdown answer for ${question.data.id}[${index}]:`, e.target.value);
                            }}
                          >
                            <option value="">Select word</option>
                            {question.data.options[index].map((option, i) => (
                              <option key={`option-${question.data.id}-${index}-${i}`} value={option}>
                                {option}
                              </option>
                            ))}
                          </select>
                        )}
                    </React.Fragment>
                  ))}
            </div>
          </div>
        </div>
      );

    case "Reading: Fill in the Blanks":
      return (
        <div className="space-y-4">
          <h3 className="font-semibold text-lg text-[var(--primary-color)]">
            {question.name}
          </h3>
          <p className="text-gray-600 mb-4">{question.description}</p>
          <div className="bg-white p-6 rounded-lg shadow-sm">
            <div className="text-gray-800 leading-relaxed mb-6">
              {question.data.paragraph &&
                question.data.paragraph
                  .split(/______\d+______/)
                  .map((part, index, array) => (
                    <React.Fragment key={`fragment-${question.data.id}-${index}`}>
                      <span>{part}</span>
                      {index < array.length - 1 && (
                        <span
                          className="mx-2 px-4 py-1 border-b-2 border-dashed border-[var(--primary-color)] min-w-[100px] inline-block"
                          onDragOver={(e) => {
                            e.preventDefault();
                            e.target.classList.add("bg-gray-100");
                          }}
                          onDragLeave={(e) => {
                            e.target.classList.remove("bg-gray-100");
                          }}
                          onDrop={(e) => {
                            e.preventDefault();
                            e.target.classList.remove("bg-gray-100");
                            const word = e.dataTransfer.getData("text");
                            // Validate word is from current question's options
                            if (question.data.options.includes(word)) {
                              const newAnswers = {
                                ...(answers[question.data.id] || {}),
                                [index]: word,
                              };
                              handleAnswerSave(question.data.id, newAnswers);
                              console.log(`Saved drag-and-drop answer for ${question.data.id}[${index}]:`, word);
                            } else {
                              console.warn(`Invalid drag-and-drop answer for ${question.data.id}:`, word);
                            }
                          }}
                        >
                          {answers[question.data.id]?.[index] || ""}
                        </span>
                      )}
                    </React.Fragment>
                  ))}
            </div>
            <div className="flex flex-wrap gap-2 p-4 bg-gray-50 rounded-lg">
              {question.data.options?.map((word, index) => (
                <div
                  key={`word-${question.data.id}-${index}`}
                  draggable
                  className={`px-3 py-1 bg-white border rounded-md cursor-move hover:shadow-md transition-all
                  ${
                    Object.values(answers[question.data.id] || {}).includes(word)
                      ? "opacity-50"
                      : ""
                  }`}
                  onDragStart={(e) => {
                    e.dataTransfer.setData("text", word);
                  }}
                >
                  {word}
                </div>
              ))}
            </div>
          </div>
        </div>
      );

    case "Multiple Choice, Single Answer":
      return (
        <div className="space-y-4">
          <h3 className="font-semibold text-lg text-[var(--primary-color)]">
            {question.name}
          </h3>
          <p className="text-gray-600 mb-4">{question.description}</p>
          <div className="bg-white p-6 rounded-lg shadow-sm">
            {question.data.passage_for_question && (
              <div className="mb-6">
                <h4 className="font-medium text-gray-700 mb-3">
                  Read the Passage:
                </h4>
                <ReadingPassageDisplay
                  passage={question.data.passage_for_question}
                  questionType="choose_the_best"
                />
              </div>
            )}
            <div className="mb-6">
              <h4 className="font-medium text-gray-700 mb-3">Question:</h4>
              <p className="text-gray-800 font-medium">{question.data.question_stem}</p>
            </div>
            <div className="space-y-3">
              {question.data.options?.map((option, index) => (
                <label
                  key={`option-${question.data.id}-${index}`}
                  className="flex items-center space-x-3 p-3 rounded-lg hover:bg-gray-50 cursor-pointer border border-gray-200 transition-colors"
                >
                  <input
                    type="radio"
                    name={`singleAnswer-${question.data.id}`}
                    value={option}
                    checked={answers[question.data.id] === option}
                    onChange={(e) => {
                      handleAnswerSave(question.data.id, e.target.value);
                      console.log(`Saved radio answer for ${question.data.id}:`, e.target.value);
                    }}
                    className="w-4 h-4 text-[var(--primary-color)] focus:ring-[var(--primary-color)]"
                  />
                  <span className="text-gray-700">{option}</span>
                </label>
              ))}
            </div>
          </div>
        </div>
      );

    case "Multiple Choice, Multiple Answers":
      return (
        <div className="space-y-4">
          <h3 className="font-semibold text-lg text-[var(--primary-color)]">
            {question.name}
          </h3>
          <p className="text-gray-600 mb-4">{question.description}</p>
          <div className="bg-white p-6 rounded-lg shadow-sm">
            {question.data.passage && (
              <div className="mb-6">
                <h4 className="font-medium text-gray-700 mb-3">
                  Read the text:
                </h4>
                <ReadingPassageDisplay passage={question.data.passage} />
              </div>
            )}
            <div className="mb-6">
              <h4 className="font-medium text-gray-700 mb-3">Question:</h4>
              <p className="text-gray-800 font-medium leading-relaxed">
                {question.data.question}
              </p>
            </div>
            <div className="space-y-3">
              {question.data.options?.map((option, index) => (
                <label
                  key={`option-${question.data.id}-${index}`}
                  className="flex items-start space-x-3 p-4 rounded-lg hover:bg-gray-50 cursor-pointer border border-gray-200"
                >
                  <input
                    type="checkbox"
                    name={`question-${question.data.id}`}
                    value={option}
                    checked={(answers[question.data.id] || []).includes(option)}
                    onChange={(e) => {
                      const currentAnswers = Array.isArray(answers[question.data.id])
                        ? answers[question.data.id]
                        : [];
                      const newAnswers = e.target.checked
                        ? [...currentAnswers, option]
                        : currentAnswers.filter((ans) => ans !== option);
                      handleAnswerSave(question.data.id, newAnswers);
                      console.log(`Saved checkbox answer for ${question.data.id}:`, newAnswers);
                    }}
                    className="w-4 h-4 mt-1 text-[var(--primary-color)]"
                  />
                  <span className="text-gray-700 flex-1">{option}</span>
                </label>
              ))}
            </div>
          </div>
        </div>
      );

    case "Re-order Paragraphs":
      return (
        <div className="space-y-4">
          <h3 className="font-semibold text-lg text-[var(--primary-color)]">
            {question.name}
          </h3>
          <p className="text-gray-600 mb-4">{question.description}</p>
          <div className="space-y-2">
            {(paragraphOrder.length
              ? paragraphOrder
              : question.data.paragraphs
            )?.map((para, index) => (
              <div
                key={`para-${question.data.id}-${index}`}
                draggable
                onDragStart={() => handleDragStart(index)}
                onDragOver={handleDragOver}
                onDrop={() => handleDrop(index)}
                className="bg-white p-4 rounded-lg shadow-sm border border-gray-200 cursor-move hover:shadow-md transition-shadow"
              >
                <div className="flex items-center gap-3">
                  <span className="text-[var(--primary-color)] font-semibold">
                    {index + 1}
                  </span>
                  <p className="text-gray-800">{para}</p>
                </div>
              </div>
            ))}
          </div>
        </div>
      );

    default:
      return <div>Question type not supported</div>;
  }
};

export default ReadingQuestionRenderer;