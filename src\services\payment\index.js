import axios from "axios";
import config from "../../config";

export const payment = {
  userProfileService: (data) => {
    return axios.get(`${config.PAYEMENT_URL}/user_profile?user_id=${data}`);
  },
  userCheckPlanStatusService: (data) => {
    return axios.get(
      `${config.PAYEMENT_URL}/check_plan_status?user_id=${data}`
    );
  },
  userActiveTrailPlanService: (data) => {
    return axios.post(`${config.PAYEMENT_URL}/active_trial_plan`, data);
  },
  userCheckTestAccessService: (data) => {
    return axios.get(
      `${config.PAYEMENT_URL}/check_test_access?user_id=${data.user_id}&test_type=${data.test_type}`
    );
  },
  userMarkTestTakenService: (data) => {
    return axios.post(`${config.PAYEMENT_URL}/mark_test_taken`, data);
  },
  userCreateOrderService: (data) => {
    return axios.post(`${config.PAYEMENT_URL}/create_order`, data);
  },
  userPaymentSuccessService: (data) => {
    return axios.post(`${config.PAYEMENT_URL}/payment_success`, data);
  },
  userUpdateTrailStatusService: (data) => {
    return axios.post(`${config.PAYEMENT_URL}/update_trial_status`, data);
  },
  userPaymentTriggerNotificationService: (data) => {
    return axios.get(`${config.PAYEMENT_URL}/trigger_notifications`, data);
  },
};
