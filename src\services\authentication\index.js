import axios from "axios";
import config from "../../config";

export const authSevice = {
  userLoginService: (data) => {
    return axios.post(`${config.LOGIN_URL}/login`, data);
  },
  sessionValidationService: (data) => {
    return axios.post(`${config.FORGET_URL}/validate_session`, data);
  },
  userRegisterService: (data) => {
    return axios.post(`${config.AUTHENTICATION_URL}/register`, data);
  },
  userForgetService: (data) => {
    return axios.post(`${config.FORGET_URL}/forgot_password`, data);
  },
  userResetPasswordService: (data) => {
    return axios.post(`${config.FORGET_URL}/reset_password`, data);
  },
  userLogoutService: (data) => {
    return axios.post(`${config.LOGIN_URL}/logout`, data);
  },
  userVerfyEmailService: (data) => {
    return axios.get(`${config.LOGIN_URL}/verify_email`, data);
  },

  
};
