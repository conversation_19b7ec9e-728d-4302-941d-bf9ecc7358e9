import { useState } from "react";
import Button from "../../components/field/Button";
import PhoneInputComponent from "../../components/field/PhoneInputComponent";
import Input from "../../components/field/Input";
import { AiOutlineEye, AiOutlineEyeInvisible } from "react-icons/ai";
import { IoArrowBack } from "react-icons/io5";
import signupImage from "../../assets/HEROO.png"; // Make sure to add your image
import { useNavigate } from "react-router-dom";

const SignUp = () => {
  const navigate = useNavigate();
  const [formData, setFormData] = useState({
    username: "",
    email: "",
    password: "",
    phone: "",
  });
  const [showPassword, setShowPassword] = useState(false);

  const handleChange = (e) => {
    const { name, value } = e.target;
    setFormData((prev) => ({
      ...prev,
      [name]: value,
    }));
  };

  const handlePhoneChange = (value) => {
    setFormData((prev) => ({
      ...prev,
      phone: value,
    }));
  };

  const togglePasswordVisibility = () => {
    setShowPassword(!showPassword);
  };

  const handleSubmit = (e) => {
    e.preventDefault();
    console.log(formData);
  };

  return (
    <div className="min-h-screen flex items-center justify-center bg-[var(--text-color)] relative">
      {/* Back Button */}
      <button
        onClick={() => navigate("/")}
        className="absolute top-4 left-4 flex items-center gap-2 text-[var(--primary-color)] hover:opacity-80 cursor-pointer transition-all"
      >
        <IoArrowBack className="w-6 h-6" />
        <span className="font-medium">Back to Home</span>
      </button>

      <div className="container mx-auto">
        <div className="flex flex-col md:flex-row items-center justify-between bg-[var(--text-color)] rounded-xl shadow-lg overflow-hidden">
          {/* Left Side - Image */}
          <div className="hidden md:block w-1/2 p-12 bg-[var(--text-color)]">
            <div className="relative h-full">
              <div className="absolute w-[400px] h-[400px] bg-[var(--primary-color)] rounded-full -z-10 opacity-10"></div>
              <img
                src={signupImage}
                alt="Sign Up"
                className="relative z-10 w-full h-full object-contain"
              />
            </div>
          </div>

          {/* Right Side - Form */}
          <div className="w-full md:w-1/2 p-8 lg:p-12">
            <div>
              <h2 className="text-3xl font-extrabold text-[var(--secondary-color)]">
                Create your account
              </h2>
            </div>
            <form className="mt-8 space-y-6" onSubmit={handleSubmit}>
              <div className="space-y-4">
                <Input
                  type="text"
                  name="username"
                  label="Username"
                  value={formData.username}
                  onChange={handleChange}
                  required
                  placeholder="Enter your username"
                />

                <Input
                  type="email"
                  name="email"
                  label="Email address"
                  value={formData.email}
                  onChange={handleChange}
                  required
                  placeholder="Enter your email"
                />

                <PhoneInputComponent
                  label="Phone Number"
                  value={formData.phone}
                  onChange={handlePhoneChange}
                  required
                />

                <Input
                  type={showPassword ? "text" : "password"}
                  name="password"
                  label="Password"
                  value={formData.password}
                  onChange={handleChange}
                  required
                  placeholder="Enter your password"
                  rightIcon={
                    <button
                      type="button"
                      onClick={togglePasswordVisibility}
                      className="focus:outline-none"
                    >
                      {showPassword ? (
                        <AiOutlineEyeInvisible className="w-5 h-10 text-[var(--primary-color)] font-bold mt-8 cursor-pointer" />
                      ) : (
                        <AiOutlineEye className="w-5 h-10 text-[var(--primary-color)] mt-8 font-bold cursor-pointer" />
                      )}
                    </button>
                  }
                />
              </div>

              <Button
                type="submit"
                name="Sign Up"
                className="w-full bg-[var(--primary-color)] text-[var(--background-color)] hover:opacity-90 text-text-color py-3 rounded-lg font-medium cursor-pointer transition-all"
              />

              <div className="text-center mt-4">
                <p className="text-sm text-gray-600">
                  Already have an account?{" "}
                  <Button
                    name="Sign In"
                    link="/signin"
                    className="text-[var(--primary-color)] cursor-pointer hover:text-opacity-80 font-medium"
                  />
                </p>
              </div>
            </form>
          </div>
        </div>
      </div>
    </div>
  );
};

export default SignUp;
