import React from "react";
import { FaStar } from "react-icons/fa";
import user1 from "../../assets/girlpose.svg";
import user2 from "../../assets/superpose.svg";

const Testimonial = () => {
  const testimonials = [
    {
      id: 1,
      name: "<PERSON>",
      position: "PTE Academic Score: 85",
      rating: 5.0,
      feedback:
        "The mock tests perfectly simulated the real exam environment. I achieved my target score in speaking and writing sections.",
      image: user1,
    },
    {
      id: 2,
      name: "<PERSON>",
      position: "PTE Academic Score: 82",
      rating: 5.0,
      feedback:
        "Comprehensive practice materials and instant feedback helped me improve my weak areas. Highly recommended for PTE preparation!",
      image: user2,
    },
  ];

  const renderStars = (rating) => {
    return [...Array(5)].map((_, index) => (
      <FaStar key={index} className="text-[var(--primary-color)]" />
    ));
  };

  return (
    <div className="bg-[#f5f5f5] py-16" id="testimonials">
      <div className="container mx-auto px-4">
        <div className="text-center mb-12">
          <span className="text-[var(--primary-color)]">
            — Client Testimonials
          </span>
          <h2 className="text-3xl font-bold mt-2">
            What My{" "}
            <span className="text-[var(--primary-color)]">
              Clients Are Saying
            </span>
          </h2>
        </div>

        <div className="relative overflow-hidden">
          <div className="flex animate-scroll gap-8 py-8">
            {[...testimonials, ...testimonials].map((testimonial, index) => (
              <div
                key={`${testimonial.id}-${index}`}
                className="min-w-[500px] bg-white rounded-xl p-8 shadow-lg"
              >
                <div className="flex items-start gap-4 mb-6">
                  <img
                    src={testimonial.image}
                    alt={testimonial.name}
                    className="w-16 h-16 rounded-full object-cover"
                  />
                  <div className="flex-1">
                    <h3 className="text-xl font-bold">{testimonial.name}</h3>
                    <p className="text-gray-500 text-sm">
                      {testimonial.position}
                    </p>
                    <div className="flex items-center gap-1 mt-2">
                      {renderStars(testimonial.rating)}
                      <span className="ml-2 text-lg font-semibold">
                        {testimonial.rating}
                      </span>
                    </div>
                  </div>
                  <span className="text-4xl text-[var(--primary-color)]">
                    "
                  </span>
                </div>
                <p className="text-gray-600 text-lg">{testimonial.feedback}</p>
              </div>
            ))}
          </div>
        </div>
      </div>
    </div>
  );
};

export default Testimonial;
