import React from "react";
import { useNavigate } from "react-router-dom";
import { motion } from "framer-motion";
import {
  Fa<PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON>a<PERSON><PERSON>,
  FaArrowRight,
  FaArrowLeft,
} from "react-icons/fa";
import {
  RiQuestionAnswerLine,
  RiPencilLine,
  RiStackLine,
  RiFileTextLine,
} from "react-icons/ri";

const ReadingInstructions = () => {
  const navigate = useNavigate();

  const sections = [
    {
      title: "Multiple Choice Questions",
      icon: <RiQuestionAnswerLine className="text-3xl" />,
      tips: [
        "Read all options carefully",
        "Eliminate wrong answers",
        "Time per question: 1.5-2 minutes",
      ],
      color: "bg-[var(--primary-color)]",
    },
    {
      title: "Fill in the Blanks",
      icon: <RiPencilLine className="text-3xl" />,
      tips: [
        "Read the entire passage first",
        "Check grammar consistency",
        "Time per question: 2-3 minutes",
      ],
      color: "bg-[var(--primary-color)]",
    },
    {
      title: "Re-order Paragraphs",
      icon: <RiStackLine className="text-3xl" />,
      tips: [
        "Identify topic sentence",
        "Look for connecting words",
        "Time per question: 2-3 minutes",
      ],
      color: "bg-[var(--primary-color)]",
    },
    {
      title: "Reading & Writing",
      icon: <RiFileTextLine className="text-3xl" />,
      tips: [
        "Understand context fully",
        "Check word collocations",
        "Time per question: 2-3 minutes",
      ],
      color: "bg-[var(--primary-color)]",
    },
  ];

  return (
    <div className="h-screen bg-[var(--background-color)] p-4 md:p-8 overflow-hidden">
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        className="h-full max-w-6xl mx-auto relative" // Added relative positioning
      >
        {/* Back Button */}
        <motion.button
          whileHover={{ scale: 1.05 }}
          whileTap={{ scale: 0.95 }}
          onClick={() => navigate("/dashboard")}
          className="absolute top-0 right-0 font-bold flex items-center gap-2 px-4 py-2 cursor-pointer text-[var(--primary-color)] hover: transition-all"
        >
          <FaArrowLeft className="text-sm" />
          <span>Back</span>
        </motion.button>

        <div className="flex h-full gap-8">
          {/* Left Side - Header, Time Management, and Buttons */}
          <div className="w-1/3 flex flex-col justify-between">
            <div>
              <div className="flex items-center gap-4 mb-6">
                <div className="bg-[var(--primary-color)] p-3 rounded-2xl">
                  <FaBookReader className="text-4xl text-white" />
                </div>
                <h1 className="text-3xl font-bold text-[var(--primary-color)]">
                  Reading Test Guide
                </h1>
              </div>

              <motion.div
                whileHover={{ scale: 1.02 }}
                className="bg-[var(--primary-color)] cursor-pointer p-6 rounded-2xl text-white"
              >
                <div className="flex items-center gap-3 mb-4">
                  <FaClock className="text-3xl" />
                  <h2 className="text-2xl font-semibold">Time Allocation</h2>
                </div>
                <div className="text-4xl font-bold mb-4">32-41</div>
                <div className="text-lg opacity-90">Minutes Total</div>
                <div className="mt-4 text-sm opacity-80">
                  Manage your time efficiently for each section
                </div>
              </motion.div>
            </div>

            {/* Navigation Buttons */}
            <div className="space-y-4 mb-8">
              <motion.button
                whileHover={{ scale: 1.02, x: -5 }}
                whileTap={{ scale: 0.95 }}
                onClick={() => navigate("/dashboard")}
                className="w-full flex items-center cursor-pointer justify-center gap-2 px-6 py-4 rounded-xl border-2 border-[var(--primary-color)] text-[var(--primary-color)] hover:bg-[var(--primary-color)] hover:text-white transition-all"
              >
                <FaArrowLeft /> Back to Dashboard
              </motion.button>
              <motion.button
                whileHover={{ scale: 1.02, x: 5 }}
                whileTap={{ scale: 0.95 }}
                onClick={() => navigate("/reading-module")}
                className="w-full flex items-center cursor-pointer justify-center gap-2 px-6 py-4 rounded-xl bg-[var(--primary-color)] text-white hover:opacity-90 transition-all"
              >
                Start Practice <FaArrowRight />
              </motion.button>
            </div>
          </div>

          {/* Right Side - Question Types */}
          <div className="w-2/3 grid grid-cols-2 gap-4 h-full content-center">
            {sections.map((section, index) => (
              <motion.div
                key={section.title}
                initial={{ opacity: 0, x: 50 }}
                animate={{ opacity: 1, x: 0 }}
                transition={{ delay: index * 0.1 }}
                className="group relative overflow-hidden"
              >
                <div
                  className={`absolute top-0 left-0 w-2 h-full ${section.color} group-hover:w-full transition-all duration-300 opacity-10`}
                />
                <div className="bg-white  p-4 shadow-lg relative z-10">
                  <div className="flex items-start gap-3">
                    <div
                      className={`${section.color} text-white p-2 rounded-xl`}
                    >
                      {section.icon}
                    </div>
                    <div className="flex-1">
                      <h3 className="text-lg font-semibold mb-2">
                        {section.title}
                      </h3>
                      <ul className="space-y-1">
                        {section.tips.map((tip, i) => (
                          <motion.li
                            key={i}
                            initial={{ opacity: 0 }}
                            animate={{ opacity: 1 }}
                            transition={{ delay: index * 0.1 + i * 0.1 }}
                            className="flex items-center gap-2 text-gray-600 text-sm"
                          >
                            <div
                              className={`w-1.5 h-1.5 rounded-full ${section.color}`}
                            />
                            {tip}
                          </motion.li>
                        ))}
                      </ul>
                    </div>
                  </div>
                </div>
              </motion.div>
            ))}
          </div>
        </div>
      </motion.div>
    </div>
  );
};

export default ReadingInstructions;
