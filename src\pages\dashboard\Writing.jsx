import TestLayout from "./components/TestLayout";

const Writing = () => {
  const academicTests = [
    {
      name: "Summarize Written Text",
      description: "Write a summary of 5-75 words from a given text",
      duration: "10 minutes",
    },
    {
      name: "Write Essay",
      description: "Write a 200-300 word essay on a given topic",
      duration: "20 minutes",
    },
  ];

  const coreTests = [
    {
      name: "Summarize Written Text",
      description: "Write a summary of 5-75 words from a given text",
      duration: "10 minutes",
    },
  ];

  return (
    <TestLayout section="Writing Practice">
      {({ examType }) => (
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          {(examType === "academic" ? academicTests : coreTests).map(
            (test, index) => (
              <div
                key={index}
                className="bg-white p-6 rounded-xl shadow-sm hover:shadow-md transition-all"
              >
                <h3 className="font-semibold text-lg mb-2">{test.name}</h3>
                <p className="text-gray-500 text-sm mb-2">{test.description}</p>
                <div className="flex items-center gap-2 text-sm text-gray-400 mb-4">
                  <span>⏱️ {test.duration}</span>
                </div>
                <div className="flex justify-between items-center">
                  <span className="text-sm text-gray-400">0 Attempts</span>
                  <button className="bg-[var(--primary-color)] text-white px-4 py-2 rounded-lg text-sm">
                    Start Practice
                  </button>
                </div>
              </div>
            )
          )}
        </div>
      )}
    </TestLayout>
  );
};

export default Writing;
