import React, { useState } from "react";

const ReadingPassageDisplay = ({ passage }) => {
  const [showPassage, setShowPassage] = useState(true);
  
  // console.log("ReadingPassageDisplay received:", {
  //   passage: passage,
  //   type: questionType,
  //   passageType: typeof passage
  // });

  // Show message for missing passage
  if (!passage) {
    return (
      <div className="bg-yellow-50 p-4 rounded-lg border border-yellow-200">
        <p className="text-yellow-800">
          Please read the question carefully. The passage will be shown here when available.
        </p>
      </div>
    );
  }

  return (
    <div className="mb-6">
      <div className="flex items-center gap-2 mb-2">
        <input
          type="checkbox"
          checked={showPassage}
          onChange={() => setShowPassage(!showPassage)}
          className="w-4 h-4 text-[var(--primary-color)]"
        />
        <label className="text-gray-700 font-medium">Show/Hide Passage</label>
      </div>
      {showPassage && (
        <div className="bg-gray-50 p-6 rounded-lg border border-gray-100">
          <div className="prose max-w-none">
            <div className="text-gray-800 leading-relaxed whitespace-pre-wrap">
              {typeof passage === 'string' ? passage : JSON.stringify(passage)}
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default ReadingPassageDisplay;
