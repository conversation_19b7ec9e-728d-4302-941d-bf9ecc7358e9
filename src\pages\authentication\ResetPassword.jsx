import { useState } from "react";
import Button from "../../components/field/Button";
import Input from "../../components/field/Input";
import { IoArrowBack } from "react-icons/io5";
import { AiOutlineEye, AiOutlineEyeInvisible } from "react-icons/ai";
import { useNavigate } from "react-router-dom";
import loginImage from "../../assets/password.svg";

import Toastify from "../../components/popup/Toastify";
import Service from "../../services/Service";

const ResetPassword = () => {
  const navigate = useNavigate();
  const [formData, setFormData] = useState({
    otp: "",
    newPassword: "",
    confirmPassword: "",
  });
  const [showPassword, setShowPassword] = useState({
    new: false,
    confirm: false,
  });
  const [res, setRes] = useState(null);

  const handleSubmit = async (e) => {
    e.preventDefault();
    if (formData.newPassword !== formData.confirmPassword) {
      setRes({ response: { data: { message: "Passwords do not match!" } } });
      return;
    }

    try {
      const email = sessionStorage.getItem("reset_email");
      if (!email) {
        navigate("/forgot-password");
        return;
      }

      const response = await Service.userResetPasswordService({
        email,
        otp: formData.otp,
        new_password: formData.newPassword,
      });

      setRes(response);
      sessionStorage.removeItem("reset_email");
      setTimeout(() => {
        navigate("/signin");
      }, 2000);
    } catch (error) {
      setRes(error);
    }
  };

  const handleChange = (e) => {
    const { name, value } = e.target;
    setFormData((prev) => ({
      ...prev,
      [name]: value,
    }));
  };

  const togglePasswordVisibility = (field) => {
    setShowPassword((prev) => ({
      ...prev,
      [field]: !prev[field],
    }));
  };

  return (
    <div className="min-h-screen flex items-center justify-center bg-[var(--text-color)] relative">
      <Toastify res={res} resClear={() => setRes(null)} />
      <button
        onClick={() => navigate("/forgot-password")}
        className="absolute cursor-pointer top-4 left-4 flex items-center gap-2 text-[var(--primary-color)] hover:opacity-80 transition-all"
      >
        <IoArrowBack className="w-6 h-6" />
        <span className="font-medium">Back</span>
      </button>

      <div className="container mx-auto">
        <div className="flex flex-col md:flex-row items-center justify-between bg-[var(--text-color)] rounded-xl shadow-lg overflow-hidden">
          <div className="w-full md:w-1/2 p-8 lg:p-12">
            <div>
              <h2 className="text-3xl font-extrabold text-[var(--secondary-color)]">
                Reset Password
              </h2>
              <p className="mt-2 text-sm text-gray-600">
                Enter the OTP sent to your email and create a new password
              </p>
            </div>

            <form className="mt-8 space-y-6" onSubmit={handleSubmit}>
              <Input
                type="text"
                name="otp"
                label="OTP"
                value={formData.otp}
                onChange={handleChange}
                required
                placeholder="Enter OTP"
                maxLength={6}
              />

              <Input
                type={showPassword.new ? "text" : "password"}
                name="newPassword"
                label="New Password"
                value={formData.newPassword}
                onChange={handleChange}
                required
                placeholder="Enter new password"
                rightIcon={
                  <button
                    type="button"
                    onClick={() => togglePasswordVisibility("new")}
                    className="focus:outline-none"
                  >
                    {showPassword.new ? (
                      <AiOutlineEyeInvisible className="w-5 h-10 text-[var(--primary-color)] font-bold mt-8 cursor-pointer" />
                    ) : (
                      <AiOutlineEye className="w-5 h-10 text-[var(--primary-color)] mt-8 font-bold cursor-pointer" />
                    )}
                  </button>
                }
              />

              <Input
                type={showPassword.confirm ? "text" : "password"}
                name="confirmPassword"
                label="Confirm Password"
                value={formData.confirmPassword}
                onChange={handleChange}
                required
                placeholder="Confirm new password"
                rightIcon={
                  <button
                    type="button"
                    onClick={() => togglePasswordVisibility("confirm")}
                    className="focus:outline-none"
                  >
                    {showPassword.confirm ? (
                      <AiOutlineEyeInvisible className="w-5 h-10 text-[var(--primary-color)] font-bold mt-8 cursor-pointer" />
                    ) : (
                      <AiOutlineEye className="w-5 h-10 text-[var(--primary-color)] mt-8 font-bold cursor-pointer" />
                    )}
                  </button>
                }
              />

              <Button
                type="submit"
                name="Reset Password"
                className="w-full cursor-pointer bg-[var(--primary-color)] text-[var(--text-color)] hover:opacity-90 py-3 rounded-lg font-medium transition-all"
              />
            </form>
          </div>

          <div className="hidden md:block w-1/2 p-12 bg-[var(--text-color)]">
            <div className="relative h-full">
              <div className="absolute w-[400px] h-[400px] bg-[var(--primary-color)] rounded-full -z-10 opacity-10"></div>
              <img
                src={loginImage}
                alt="Reset Password"
                className="relative z-10 w-full h-full object-contain"
              />
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default ResetPassword;
