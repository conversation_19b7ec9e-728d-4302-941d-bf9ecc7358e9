import PropTypes from "prop-types";
import { Link } from "react-router-dom";

const Button = ({
  name,
  type,
  title,
  onClick,
  disabled,
  className,
  icon,
  link,
  loading,
}) => {
  if (link) {
    return (
      <Link
        to={link}
        className={`${
          className
            ? className
            : `w-full text-white py-3 rounded-lg font-medium transition-all flex justify-center items-center space-x-2 ${
                disabled || loading
                  ? "bg-gray-300 cursor-not-allowed"
                  : "bg-[#e79686] hover:bg-[#E2725B]"
              }`
        }`}
        title={title}
      >
        {icon && <span>{icon}</span>}
        <span>{name}</span>
        {loading && (
          <div className="animate-spin rounded-full h-5 w-5 border-b-2 border-white"></div>
        )}
      </Link>
    );
  }

  return (
    <button
      type={type}
      title={title}
      onClick={onClick}
      disabled={disabled || loading}
      className={`${
        className
          ? className
          : `w-full text-white py-3 rounded-lg font-medium transition-all flex justify-center cursor-pointer items-center space-x-2 ${
              disabled || loading
                ? "bg-gray-300 cursor-not-allowed"
                : "bg-[#e79686] hover:bg-[#E2725B]"
            }`
      }`}
    >
      {icon && <span>{icon}</span>}
      <span>{name}</span>
      {loading && (
        <div className="animate-spin rounded-full h-5 w-5 border-b-2 border-white"></div>
      )}
    </button>
  );
};

Button.propTypes = {
  name: PropTypes.string.isRequired, // Name is usually required for a button
  type: PropTypes.oneOf(["button", "submit", "reset"]),
  title: PropTypes.string,
  onClick: PropTypes.func,
  disabled: PropTypes.bool,
  className: PropTypes.string,
  icon: PropTypes.node,
  link: PropTypes.string,
  loading: PropTypes.bool,
};

export default Button;
