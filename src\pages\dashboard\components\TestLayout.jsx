import { useState } from "react";
import Button from "../../../components/field/Button";

const TestLayout = ({ section, children }) => {
  const [examType, setExamType] = useState("academic");

  return (
    <div>
      <div className="flex justify-between items-center mb-8">
        <h1 className="text-2xl font-bold">{section}</h1>
        <div className="flex gap-4">
          <Button
            name="Academic"
            onClick={() => setExamType("academic")}
            className={`px-6 py-2 rounded-lg ${
              examType === "academic"
                ? "bg-[var(--primary-color)] text-white"
                : "bg-gray-100 text-gray-600"
            }`}
          />
          <Button
            name="Core"
            onClick={() => setExamType("core")}
            className={`px-6 py-2 rounded-lg ${
              examType === "core"
                ? "bg-[var(--primary-color)] text-white"
                : "bg-gray-100 text-gray-600"
            }`}
          />
        </div>
      </div>

      {children({ examType })}
    </div>
  );
};

export default TestLayout;
